import {DeepPartial, ID} from '@vendure/common/lib/shared-types';
import {Channel, Product, VendureEntity} from '@vendure/core';
import {EntityId} from '@vendure/core/dist/entity/entity-id.decorator';
import {Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne} from 'typeorm';
import {ForumPostStatus, ForumPostType} from '../generated-admin-types';
import {ForumActivity} from './forum-activity.entity';
import {ForumCustomer} from './forum-customer.entity';
import {ForumTag} from './forum-tag.entity';

@Entity()
export class ForumPost extends VendureEntity {
  constructor(input?: DeepPartial<ForumPost>) {
    super(input);
  }

  @Index()
  @Column({type: Date, nullable: true})
  deletedAt: Date | null;

  // 帖子标题
  @Index()
  @Column({type: 'varchar', nullable: false, comment: '帖子标题'})
  title: string;

  // 帖子内容
  @Column({type: 'text', nullable: false, comment: '帖子内容'})
  content: string;

  // 帖子图片组
  @Column({type: 'simple-array', nullable: true, comment: '帖子图片组'})
  images: string[];

  // 帖子主图
  @Column({type: 'varchar', nullable: true, comment: '帖子主图'})
  mainImage: string;

  // 帖子类型 长文/ 短文
  @Index()
  @Column({type: 'varchar', nullable: false, default: ForumPostType.Short, comment: '帖子类型'})
  type: ForumPostType;

  // 帖子状态
  @Index()
  @Column({type: 'varchar', nullable: false, comment: '帖子状态'})
  status: ForumPostStatus;

  // 发布时间
  @Index()
  @Column({type: 'datetime', nullable: true, comment: '发布时间'})
  releaseTime: Date;

  // 审核时间
  @Index()
  @Column({type: 'datetime', nullable: true, comment: '审核时间'})
  auditTime: Date;

  // 拒绝原因
  @Index()
  @Column({type: 'varchar', nullable: true, comment: '拒绝原因'})
  refuseReason: string;

  // 分享数
  @Column({type: 'int', nullable: false, default: 0, comment: '分享数'})
  shareCount: number;

  // 是否心愿帖
  @Index()
  @Column({type: 'boolean', nullable: false, default: false, comment: '是否心愿帖'})
  isWish: boolean;

  // 关联心愿帖
  @ManyToOne(type => ForumPost, {nullable: true})
  @JoinColumn({name: 'wishPostId'})
  forumWishPost: ForumPost;

  @EntityId({nullable: true})
  wishPostId?: ID;

  // 帖子作者
  @ManyToOne(type => ForumCustomer, {nullable: true})
  forumCustomer: ForumCustomer;

  // 帖子作者ID
  @EntityId({nullable: true})
  forumCustomerId?: ID;

  // 帖子话题
  @ManyToMany(type => ForumTag)
  @JoinTable()
  forumTags: ForumTag[];

  // 帖子关联的活动
  @ManyToMany(type => ForumActivity, {nullable: true})
  @JoinTable()
  forumActivities: ForumActivity[];

  // 帖子关联商品
  @ManyToMany(type => Product)
  @JoinTable()
  products: Product[];

  // 同步到NodeBB后的帖子ID
  @Index()
  @Column({type: 'bigint', nullable: true, comment: '同步到NodeBB后的帖子ID'})
  nodeBBPostId: number;

  // 同步到NodeBB后的topicID
  @Index()
  @Column({type: 'bigint', nullable: true, comment: '同步到NodeBB后的topicID'})
  nodeBBTopicId: number;

  // 关联渠道
  @ManyToMany(type => Channel)
  @JoinTable()
  channels: Channel[];

  // 分享标题
  @Index()
  @Column({type: 'varchar', nullable: true, comment: '分享标题'})
  shareTitle: string;

  // 分享封面图片
  @Index()
  @Column({nullable: true, type: 'varchar', comment: '分享图片'})
  shareImg: string;

  tid: number;

  downVotes: number;

  upVotes: number;

  votes: number;

  viewCount: number;

  mainPid: number;

  postCount: number;

  visitorsCount: number;

  pageViews: number;

  pinned: boolean;
}
