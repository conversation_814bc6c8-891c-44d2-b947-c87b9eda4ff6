import {CrudPermissionDefinition} from '@vendure/core';

export const ForumTagOperate = new CrudPermissionDefinition(
  'ForumTag',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建话题';
      case 'read':
        return '查看话题';
      case 'update':
        return '编辑话题';
      case 'delete':
        return '删除话题';
    }
  },
);

export const ForumPostOperate = new CrudPermissionDefinition(
  'ForumPost',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建帖子';
      case 'read':
        return '查看帖子';
      case 'update':
        return '编辑帖子';
      case 'delete':
        return '删除帖子';
    }
  },
);

export const ForumActivityOperate = new CrudPermissionDefinition(
  'ForumActivity',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建论坛活动';
      case 'read':
        return '查看论坛活动';
      case 'update':
        return '编辑论坛活动';
      case 'delete':
        return '删除论坛活动';
    }
  },
);

export const ForumCustomerOperate = new CrudPermissionDefinition(
  'ForumCustomer',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建论坛用户';
      case 'read':
        return '查看论坛用户';
      case 'update':
        return '编辑论坛用户';
      case 'delete':
        return '删除论坛用户';
    }
  },
);

export const ForumReviewOperate = new CrudPermissionDefinition(
  'ForumReview',
  (operation: 'create' | 'read' | 'update' | 'delete') => {
    switch (operation) {
      case 'create':
        return '创建评论';
      case 'read':
        return '查看评论';
      case 'update':
        return '编辑评论';
      case 'delete':
        return '删除评论';
    }
  },
);
