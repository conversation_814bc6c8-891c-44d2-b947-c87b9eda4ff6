import {DeepPartial} from '@vendure/common/lib/shared-types';
import {VendureEntity} from '@vendure/core';
import {Column, Entity, Index} from 'typeorm';

/**
 * 报表模板实体
 */
@Entity()
export class ReportTemplate extends VendureEntity {
  constructor(input?: DeepPartial<ReportTemplate>) {
    super(input);
  }

  @Index()
  @Column({type: 'varchar', length: 255, nullable: false, comment: '报表模版名'})
  templateName: string;

  @Column({type: 'json', nullable: false, comment: '筛选条件配置'})
  filterConfig: any;

  @Column({type: 'json', nullable: false, comment: '表头配置'})
  headerConfig: any;

  @Column({type: 'text', nullable: false, comment: '动态SQL'})
  sqlStatement: string;

  @Column({type: 'datetime', default: () => 'CURRENT_TIMESTAMP', comment: '创建时间'})
  createdAt: Date;

  @Column({
    type: 'datetime',
    nullable: true,
    default: null,
    onUpdate: 'CURRENT_TIMESTAMP',
    comment: '更新时间',
  })
  updatedAt: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: '测试状态，true表示已测试且有数据',
  })
  status: boolean;
}
