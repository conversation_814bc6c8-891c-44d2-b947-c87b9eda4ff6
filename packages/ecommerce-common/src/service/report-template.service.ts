import {Injectable, Logger, OnModuleD<PERSON>roy} from '@nestjs/common';
import {
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UserInputError,
} from '@vendure/core';
import {DataSource} from 'typeorm';
import {ReportTemplate} from '../entities';
import {FilterConfig, HeaderConfig, HeaderMeta} from '../types/report-template.types';

@Injectable()
export class ReportTemplateService implements OnModuleDestroy {
  private readOnlyConnection: DataSource | null = null;

  constructor(private connection: TransactionalConnection, private listQueryBuilder: ListQueryBuilder) {}

  /**
   * 清理资源
   */
  async onModuleDestroy() {
    if (this.readOnlyConnection) {
      try {
        await this.readOnlyConnection.destroy();
        console.log('只读数据库连接已关闭');
      } catch (error) {
        console.error('关闭只读数据库连接失败:', error);
      }
    }
  }

  /**
   * 创建只读数据库连接
   * 参考 vendure-config-admin.ts 中的 dbConnectionOptions 配置
   */
  private async getReadOnlyConnection(): Promise<any> {
    if (!this.readOnlyConnection?.isInitialized) {
      try {

        // 参考现有 Vendure 配置，创建只读数据库连接配置
        const readOnlyConfig = {
          type: 'mysql' as const,
          host: process.env.DB_HOST,
          port: +(process.env.DB_PORT || '3306'),
          username: process.env.REPORT_DB_USERNAME || 'test1',
          password: process.env.REPORT_DB_PASSWORD || '1q2w5y',
          database: process.env.DB_NAME,
          timezone: '+08:00',
          charset: 'utf8mb4',
          poolSize: 50, // 较小的连接池，适合只读查询
          // 禁用同步和迁移，确保只读安全
          synchronize: false,
          migrationsRun: false,
          logging: false,
          // 添加连接超时配置
          acquireTimeout: 30000,
          timeout: 30000,
        };

        this.readOnlyConnection = new DataSource(readOnlyConfig);
        await this.readOnlyConnection.initialize();

        console.log('=== 只读数据库连接已建立 ===');
        console.log(`连接用户: ${readOnlyConfig.username}`);
        console.log(`数据库: ${readOnlyConfig.database}`);
        console.log(`主机: ${readOnlyConfig.host}:${readOnlyConfig.port}`);
        console.log('连接池大小:', readOnlyConfig.poolSize);
      } catch (error) {
        console.error('创建只读数据库连接失败:', error);
        throw new UserInputError('无法连接到只读数据库');
      }
    }
    return this.readOnlyConnection;
  }

  /**
   * 查询所有报表模板
   */
  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<ReportTemplate>,
    relations?: RelationPaths<ReportTemplate>,
  ) {
    const qb = this.listQueryBuilder.build(ReportTemplate, options, {
      ctx,
      relations: relations,
    });

    const [items, totalItems] = await qb.getManyAndCount();

    // 打印查询结果，用于调试
    console.log('=== 报表模板查询接口请求参数 ===');
    console.log('RequestContext:', ctx);
    console.log('=== 报表模板查询结果 ===');
    console.log('items:', JSON.stringify(items, null, 2));

    // 移除了createdBy相关逻辑

    return {
      items,
      totalItems,
    };
  }

  /**
   * 查询单个报表模板
   */
  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<ReportTemplate>,
    relations?: RelationPaths<ReportTemplate>,
  ) {
    const qb = this.listQueryBuilder.build(ReportTemplate, options, {
      ctx,
      relations: relations,
    });
    qb.andWhere(`${qb.alias}.id = :id`, {id});
    const reportTemplate = await qb.getOne();

    // 移除了createdBy相关逻辑

    return reportTemplate;
  }

  /**
   * 创建报表模板
   */
  async create(ctx: RequestContext, input: any) {
    // 解析JSON字符串为对象
    const filterConfig = typeof input.filterConfig === 'string' ? JSON.parse(input.filterConfig) : input.filterConfig;
    const headerConfig = typeof input.headerConfig === 'string' ? JSON.parse(input.headerConfig) : input.headerConfig;

    const reportTemplate = new ReportTemplate({
      templateName: input.templateName,
      filterConfig: filterConfig as FilterConfig,
      headerConfig: headerConfig as HeaderConfig,
      sqlStatement: input.sqlStatement,
      status: false, // 默认设置为未测试状态
    });

    // 验证SQL语句是否包含{whereClause}占位符
    this.validateSqlStatement(reportTemplate.sqlStatement);

    // 验证表头配置与SQL结果集一致性
    this.validateHeaderConfig(reportTemplate.headerConfig, reportTemplate.sqlStatement);

    return this.connection.getRepository(ctx, ReportTemplate).save(reportTemplate);
  }

  /**
   * 更新报表模板
   */
  async update(ctx: RequestContext, input: any) {
    const reportTemplate = await this.findOne(ctx, input.id as string);
    if (!reportTemplate) {
      throw new UserInputError(`报表模板不存在: ${input.id}`);
    }

    // 解析JSON字符串为对象
    const filterConfig = typeof input.filterConfig === 'string' ? JSON.parse(input.filterConfig) : input.filterConfig;
    const headerConfig = typeof input.headerConfig === 'string' ? JSON.parse(input.headerConfig) : input.headerConfig;

    reportTemplate.templateName = input.templateName;
    reportTemplate.filterConfig = filterConfig as FilterConfig;
    reportTemplate.headerConfig = headerConfig as HeaderConfig;
    reportTemplate.sqlStatement = input.sqlStatement;
    reportTemplate.status = false; // 修改模板后重置为未测试状态

    // 验证SQL语句是否包含{whereClause}占位符
    this.validateSqlStatement(reportTemplate.sqlStatement);

    // 验证表头配置与SQL结果集一致性
    this.validateHeaderConfig(reportTemplate.headerConfig, reportTemplate.sqlStatement);

    return this.connection.getRepository(ctx, ReportTemplate).save(reportTemplate);
  }

  /**
   * 删除报表模板
   */
  async delete(ctx: RequestContext, id: ID) {
    const reportTemplate = await this.findOne(ctx, id);
    if (!reportTemplate) {
      throw new UserInputError(`报表模板不存在: ${id}`);
    }

    await this.connection.getRepository(ctx, ReportTemplate).remove(reportTemplate);
    return true;
  }

  /**
   * 验证SQL语句
   */
  private validateSqlStatement(sqlStatement: string) {
    // 检查SQL语句是否以SELECT开头（忽略大小写和前导空格）
    if (!/^\s*SELECT\s+/i.test(sqlStatement)) {
      throw new UserInputError('SQL语句必须是SELECT查询', {
        code: 'INVALID_SQL_TYPE',
      });
    }

    // 检查SQL语句是否包含{whereClause}占位符
    if (!sqlStatement.includes('{whereClause}')) {
      throw new UserInputError('SQL语句必须包含{whereClause}占位符，用于添加补充条件');
    }

    // 检查SQL语句是否包含WHERE子句
    if (!/\bWHERE\b/i.test(sqlStatement)) {
      throw new UserInputError('SQL语句必须包含WHERE子句，{whereClause}仅用于添加补充条件');
    }

    // 检查SQL注入风险
    const riskPatterns = [
      /;\s*DROP/i,
      /;\s*DELETE/i,
      /;\s*UPDATE/i,
      /;\s*INSERT/i,
      /;\s*ALTER/i,
      /;\s*CREATE/i,
      /EXECUTE\s+/i,
      /EXEC\s+/i,
      /INTO\s+OUTFILE/i,
      /INTO\s+DUMPFILE/i,
      // 禁止多语句查询
      /;\s*SELECT/i,
      // 禁止使用UNION，防止SQL注入
      /UNION\s+ALL/i,
      /UNION\s+SELECT/i,
    ];

    for (const pattern of riskPatterns) {
      if (pattern.test(sqlStatement)) {
        throw new UserInputError('SQL语句存在安全风险', {
          code: 'SQL_INJECTION_RISK',
        });
      }
    }
  }

  /**
   * 验证表头配置
   */
  private validateHeaderConfig(headerConfig: HeaderConfig, sqlStatement: string) {
    try {
      if (!Array.isArray(headerConfig)) {
        throw new UserInputError('表头配置必须是数组');
      }

      // 验证每个表头项是否包含必要的字段
      for (const header of headerConfig) {
        if (!header.field || !header.name) {
          throw new UserInputError('表头配置项必须包含field和name字段');
        }
      }
    } catch (error) {
      throw new UserInputError(`表头配置验证失败: ${error.message}`);
    }
  }

  /**
   * 解析表头配置
   */
  private parseHeaderConfig(headerConfig: HeaderConfig): HeaderMeta[] {
    try {
      return headerConfig.map(header => ({
        field: header.field,
        name: header.name,
      }));
    } catch (error) {
      throw new UserInputError(`表头配置解析失败: ${error.message}`);
    }
  }
}
