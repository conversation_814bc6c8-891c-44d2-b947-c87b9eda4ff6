import {Args, Mutation, Query, Resolver} from '@nestjs/graphql';
import {Allow, Ctx, ID, ListQueryOptions, RelationPaths, Relations, RequestContext, Transaction} from '@vendure/core';
import {ReportTemplate} from '../entities';
import {ReportTemplateService} from '../service';

@Resolver()
export class ReportTemplateResolver {
  constructor(private reportTemplateService: ReportTemplateService) {}

  @Query()
  @Allow('ReadReportTemplate')
  async reportTemplates(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<ReportTemplate>,
    @Relations({entity: ReportTemplate})
    relations: RelationPaths<ReportTemplate>,
  ) {
    return this.reportTemplateService.findAll(ctx, options, relations);
  }

  @Query()
  @Allow('ReadReportTemplate')
  async reportTemplate(
    @Ctx() ctx: RequestContext,
    @Args('id') id: ID,
    @Relations({entity: ReportTemplate})
    relations: RelationPaths<ReportTemplate>,
  ) {
    return this.reportTemplateService.findOne(ctx, id, undefined, relations);
  }

  @Query()
  @Allow('ExecuteReport')
  async executeReport(@Ctx() ctx: RequestContext, @Args('input') input: any) {
    return this.reportTemplateService.executeReport(ctx, input);
  }

  @Transaction()
  @Mutation()
  @Allow('CreateReportTemplate')
  async createReportTemplate(@Ctx() ctx: RequestContext, @Args('input') input: any) {
    return this.reportTemplateService.create(ctx, input);
  }

  @Transaction()
  @Mutation()
  @Allow('UpdateReportTemplate')
  async updateReportTemplate(@Ctx() ctx: RequestContext, @Args('input') input: any) {
    return this.reportTemplateService.update(ctx, input);
  }

  @Transaction()
  @Mutation()
  @Allow('DeleteReportTemplate')
  async deleteReportTemplate(@Ctx() ctx: RequestContext, @Args('id') id: ID) {
    return this.reportTemplateService.delete(ctx, id);
  }
}
