import {createTestEnvironment, registerInitializer, SqljsInitializer} from '@vendure/testing';
import {DefaultLogger, LogLevel, mergeConfig} from '@vendure/core';
import {testConfig} from '@vendure/testing/lib/test-config';
import {EcommerceCommonPlugin} from '../ecommerce-common.plugin';
import path from 'path';

registerInitializer('sqljs', new SqljsInitializer(path.join(__dirname, '__data__')));

describe('ReportTemplate', () => {
  const {server, adminClient, shopClient} = createTestEnvironment(
    mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      plugins: [EcommerceCommonPlugin],
    }),
  );

  beforeAll(async () => {
    await server.init({
      initialData: {
        defaultLanguage: 'zh_Hans',
        defaultZone: 'Asia',
        countries: [
          {
            code: 'CN',
            name: 'China',
            zone: 'Asia',
            enabled: true,
            translations: [],
          },
        ],
      },
      productsCsvPath: path.join(__dirname, 'fixtures/products.csv'),
    });
  });

  afterAll(async () => {
    await server.destroy();
  });

  it('should create a report template', async () => {
    await adminClient.asSuperAdmin();

    const CREATE_REPORT_TEMPLATE = `
      mutation CreateReportTemplate($input: ReportTemplateInput!) {
        createReportTemplate(input: $input) {
          id
          templateName
          filterConfig
          headerConfig
          sqlStatement
          createdAt
          status
        }
      }
    `;

    const result = await adminClient.query(CREATE_REPORT_TEMPLATE, {
      input: {
        templateName: '测试报表模板',
        filterConfig: JSON.stringify([
          {
            field: 'customerId',
            name: '客户ID',
            type: 'number',
          },
          {
            field: 'createdAt',
            name: '创建时间',
            type: 'date',
          },
        ]),
        headerConfig: JSON.stringify([
          {
            field: 'customerId',
            name: '客户ID',
          },
          {
            field: 'phoneNumber',
            name: '手机号',
          },
          {
            field: 'issuedAmount',
            name: '发放金额',
          },
          {
            field: 'deductedAmount',
            name: '扣减金额',
          },
          {
            field: 'totalAmount',
            name: '总金额',
          },
          {
            field: 'isMember',
            name: '是否会员',
          },
          {
            field: 'createdAt',
            name: '创建时间',
          },
        ]),
        sqlStatement: `
          SELECT    
            history.customerId AS customerId,
            customer.phoneNumber AS phoneNumber,
            SUM(CASE WHEN history.sourceType != 'orderDeduction' THEN history.amount ELSE 0 END) AS issuedAmount,
            SUM(CASE WHEN history.sourceType = 'orderDeduction' THEN ABS(history.amount) ELSE 0 END) AS deductedAmount,
            SUM(history.amount) AS totalAmount,
            CASE WHEN member.id IS NOT NULL AND member.state = 'normal' THEN 1 ELSE 0 END AS isMember,
            MIN(history.createdAt) AS createdAt
          FROM 
            virtual_currency_history history
          LEFT JOIN 
            customer ON history.customerId = customer.id
          LEFT JOIN 
            member ON history.customerId = member.customerId
          WHERE 1=1 {whereClause}
          GROUP BY 
            history.customerId, customer.phoneNumber, isMember
          ORDER BY 
            createdAt DESC
        `,
      },
    });

    expect(result.createReportTemplate.id).toBeDefined();
    expect(result.createReportTemplate.templateName).toBe('测试报表模板');
  });

  it('should execute a report query', async () => {
    await adminClient.asSuperAdmin();

    // 先获取报表模板列表
    const GET_REPORT_TEMPLATES = `
      query GetReportTemplates {
        reportTemplates {
          items {
            id
            templateName
          }
          totalItems
        }
      }
    `;

    const templatesResult = await adminClient.query(GET_REPORT_TEMPLATES);
    const templateId = templatesResult.reportTemplates.items[0].id;

    // 执行报表查询
    const EXECUTE_REPORT = `
      query ExecuteReport($input: ReportQueryInput!) {
        executeReport(input: $input) {
          headers {
            field
            name
          }
          rows
          stats {
            executionTime
            totalRows
          }
        }
      }
    `;

    const result = await adminClient.query(EXECUTE_REPORT, {
      input: {
        templateId,
        filters: [],
        pagination: {
          page: 1,
          pageSize: 10,
        },
      },
    });

    expect(result.executeReport.headers).toBeDefined();
    expect(result.executeReport.rows).toBeDefined();
    expect(result.executeReport.stats).toBeDefined();
  });
});
