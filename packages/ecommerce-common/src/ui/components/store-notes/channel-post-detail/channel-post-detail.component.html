<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left> </vdr-ab-left>
    <vdr-ab-right>
      <div class="flex items-center flex-wrap">
        <button
          *vdrIfPermissions="['CreateForumPost', 'UpdateForumPost']"
          [disabled]="detailForm.invalid || detailForm.pristine"
          class="btn btn-primary"
          (click)="save(true)"
        >
          立即发布
        </button>
        <button
          *vdrIfPermissions="['CreateForumPost', 'UpdateForumPost']"
          [disabled]="detailForm.invalid || detailForm.pristine"
          class="btn"
          (click)="save()"
        >
          保存草稿
        </button>
        <button class="btn" [routerLink]="['../']">返回</button>
      </div>
    </vdr-ab-right>
  </vdr-action-bar>

  <!-- <clr-tabs>
    <clr-tab *ngFor="let item of saveTypes">
      <button [class]="{active: curType === item.value}" (click)="saveTypeChange(item.value)" clrTabLink>
        {{ item.label }}
      </button>
    </clr-tab>
  </clr-tabs> -->

  <div class="card mt0" [formGroup]="detailForm">
    <div class="card-block post-form-container">
      <app-form-field label="文章类型">
        <clr-radio-container clrInline>
          <clr-radio-wrapper>
            <input type="radio" formControlName="type" value="short" clrRadio />
            <label>短文</label>
          </clr-radio-wrapper>
          <clr-radio-wrapper>
            <input type="radio" formControlName="type" value="long" clrRadio />
            <label>长文</label>
          </clr-radio-wrapper>
        </clr-radio-container>
      </app-form-field>
      <app-form-field label="选择发文账号">
        <app-select-async
          [gql]="FORUM_CUSTOMER_LIST"
          [filter]="forumCustomerFilter"
          formControlName="forumCustomerId"
        ></app-select-async>
      </app-form-field>
      <app-form-field *ngIf="isShort" label="帖子图片（图片尺寸比例：UI提供，支持JPG、PNG、GIF）">
        <!-- <image-picker [multiple]="true" formControlName="images"></image-picker> -->
        <app-image-file-input
          formControlName="images"
          [isMulti]="true"
          [max]="9"
          (change)="handleSelectImage()"
        ></app-image-file-input>
      </app-form-field>
      <app-form-field label="帖子标题（最多输入25个字）">
        <input type="text" formControlName="title" [maxLength]="25" />
      </app-form-field>
      <app-form-field *ngIf="!isShort" label="帖子主图（图片尺寸比例：UI提供，支持JPG、PNG、GIF）">
        <!-- <image-picker formControlName="mainImage"></image-picker> -->
        <app-image-file-input formControlName="mainImage" (change)="handleSelectImage()"></app-image-file-input>
      </app-form-field>
      <app-form-field label="关联话题">
        <app-select-async [gql]="TOPIC_LIST" formControlName="forumTagIds"></app-select-async>
      </app-form-field>
      <app-form-field label="种草商品" *ngIf="seriesNonFilterParam.collectionId">
        <app-select-async
          [gql]="SERIES_PRODUCTS"
          [nonOptionsParams]="seriesNonFilterParam"
          formControlName="productIds"
        ></app-select-async>
      </app-form-field>
      <app-form-field label="帖子封面（图片尺寸比例：5:4，支持JPG、PNG、GIF）">
        <!-- <image-picker formControlName="mainImage"></image-picker> -->
        <app-image-file-input formControlName="shareImg" (change)="handleSelectImage()"></app-image-file-input>
      </app-form-field>
      <app-form-field label="帖子正文">
        <div class="post-rich-text-container w-100">
          <cus-rich-text-editor formControlName="content"></cus-rich-text-editor>
        </div>
      </app-form-field>
    </div>
  </div>
</vdr-page-block>
