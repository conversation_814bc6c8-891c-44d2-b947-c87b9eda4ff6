/* eslint-disable @typescript-eslint/no-explicit-any */
import {Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef} from '@angular/core';
import {AbstractControl, FormBuilder, FormGroup, Validators, ValidatorFn} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {
  BaseDetailComponent,
  DataService,
  LanguageCode,
  NotificationService,
  ServerConfigService,
} from '@vendure/admin-ui/core';
import {FORUM_TAG_LIST, UPSERT_FORUM_POST, FORUM_CUSTOMER_LIST} from '../graphql';
import {RECOMMEND_SETTING_LIST, SERIES_PRODUCTS} from '../../../graphql/graphql';

@Component({
  selector: 'app-channel-post-detail',
  templateUrl: './channel-post-detail.component.html',
  styleUrls: ['./channel-post-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChannelPostDetailComponent extends BaseDetailComponent<any> implements OnInit {
  detailForm: FormGroup;
  isCopy: boolean;

  TOPIC_LIST = FORUM_TAG_LIST;
  FORUM_CUSTOMER_LIST = FORUM_CUSTOMER_LIST;
  forumCustomerFilter = {createType: {eq: 'admin'}};
  saveTypes = [
    {
      label: '短文',
      value: 'short',
    },
    {
      label: '长文',
      value: 'long',
    },
  ];

  SERIES_PRODUCTS = SERIES_PRODUCTS;
  seriesNonFilterParam = {collectionId: ''};

  get curType() {
    return this.detailForm?.controls?.type?.getRawValue();
  }

  get isShort() {
    return this.curType === 'short';
  }

  constructor(
    route: ActivatedRoute,
    router: Router,
    protected dataService: DataService,
    private fb: FormBuilder,
    private notification: NotificationService,
    protected serverCfg: ServerConfigService,
    private ref: ChangeDetectorRef,
  ) {
    super(route, router, serverCfg, dataService);
    this.detailForm = this.fb.group(
      {
        title: [undefined, Validators.required],
        content: undefined,
        mainImage: undefined,
        images: undefined,
        type: 'short',
        forumCustomerId: [undefined, Validators.required],
        forumTagIds: undefined,
        productIds: undefined,
        shareImg: undefined,
      },
      {
        validator: this.formValidator(),
      },
    );
  }

  ngOnInit() {
    this.detailForm.controls.content.setValidators([Validators.required]);
    this.detailForm.updateValueAndValidity();
    this.getGrassProduct();
    this.route.queryParams.subscribe(params => {
      this.isCopy = params['copy'];
    });
    this.init();
  }

  protected setFormValues(entity: any, languageCode: LanguageCode): void {
    if (!entity?.id) {
      return;
    }
    const {title, content, mainImage, images, type, forumCustomerId, forumTags, products, shareImg} = entity;
    this.detailForm.patchValue({
      title,
      content,
      mainImage,
      images,
      type,
      forumCustomerId,
      forumTagIds: forumTags?.length ? forumTags[0]?.id : undefined,
      productIds: products?.length ? products[0]?.id : undefined,
      shareImg,
    });
  }

  formValidator(): ValidatorFn {
    return (ctrl: AbstractControl) => {
      const val = ctrl.getRawValue();
      if (val.type === 'short' && !val.images?.length) {
        return {requireImages: true};
      }
      if (val.type === 'long' && !val.mainImage) {
        return {requireMainImage: true};
      }
      return null;
    };
  }

  getGrassProduct() {
    this.dataService
      .query<{settings: any}>(RECOMMEND_SETTING_LIST, {
        keyNames: ['forumRecommendProductGroup'],
      })
      .mapSingle(res => res.settings as any[])
      .subscribe(settings => {
        if (settings.length) {
          const res = settings[0];
          this.seriesNonFilterParam.collectionId = res.value;
        }
      });
  }

  handleSelectImage() {
    this.detailForm.markAsDirty();
    this.ref.detectChanges();
  }

  saveTypeChange(type: string) {
    this.detailForm.controls.type.setValue(type);
  }

  save(isPublish = false) {
    const {mainImage, images, forumTagIds, productIds, ...rest} = this.detailForm.getRawValue();
    const input = {
      id: this.isCopy ? undefined : this.id ? this.id : undefined,
      mainImage: this.isShort ? undefined : mainImage,
      images: this.isShort ? images : undefined,
      isPublish,
      forumTagIds: forumTagIds ? [forumTagIds] : [],
      productIds: productIds ? [productIds] : [],
      ...rest,
    };
    console.log(input);
    this.dataService.mutate(UPSERT_FORUM_POST, {input}).subscribe(res => {
      if (res) {
        this.notification.success('操作成功');
        this.router.navigate(['../'], {relativeTo: this.route}).catch(e => {
          console.log(e);
        });
      }
    });
  }
}
