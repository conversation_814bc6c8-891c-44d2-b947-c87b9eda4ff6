import gql from 'graphql-tag';

export const FORUM_TAG_FRAGMENT = gql`
  fragment ForumTagFragment on ForumTag {
    id
    createdAt
    updatedAt
    name
    remark
    description
    image
    smallProgramQRCodeLink
    status
    postCount
    replyCount
    forumTagActivity {
      id
      name
    }
    sort
    tagHash
    shareImg
    visitorsCount
  }
`;

export const FORUM_ACTIVITY_FRAGMENT = gql`
  fragment ForumActivityFragment on ForumActivity {
    id
    createdAt
    updatedAt
    deletedAt
    name
    remark
    description
    smallProgramQRCodeLink
    status
    startTime
    endTime
    forumTag {
      id
      name
    }
    forumTagId
    tagHash
  }
`;

export const FORUM_POST_FRAGMENT = gql`
  fragment ForumPostFragment on ForumPost {
    id
    uid
    nodeBBPostId
    createdAt
    updatedAt
    deletedAt
    type
    title
    content
    images
    mainImage
    status
    releaseTime
    auditTime
    refuseReason
    forumCustomerId
    forumCustomer {
      id
      name
      phone
      createType
      headPortrait
    }
    forumTags {
      id
      name
      tagHash
    }
    forumActivities {
      id
      name
      tagHash
    }
    products {
      id
      name
      featuredAsset {
        id
        preview
      }
      customFields {
        price
      }
    }
    forumWishPost {
      id
      title
    }
    isWish
    upVotes
    viewCount
    shareImg
    visitorsCount
    pageViews
    pinned
  }
`;

export const FORUM_REVIEW_FRAGMENT = gql`
  fragment ForumReviewFragment on ForumReview {
    id
    createdAt
    updatedAt
    deletedAt
    content
    images
    status
    auditTime
    refuseReason
    forumPostId
    forumPost {
      ...ForumPostFragment
    }
    postId
    toPostId
    topicId
    forumCustomer {
      id
      name
      phone
      headPortrait
    }
    parentForumCustomer {
      id
      name
      phone
      headPortrait
    }
    upVotes
    reviewTime
    level
  }
  ${FORUM_POST_FRAGMENT}
`;
