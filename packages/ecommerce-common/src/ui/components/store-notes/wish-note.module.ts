import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {ComponentSharedModule} from '../../component-shared.module';

import {ChannelPostDetailComponent} from './channel-post-detail/channel-post-detail.component';
import {ChannelPostListComponent} from './channel-post-list/channel-post-list.component';
import {CustomerPostListComponent} from './customer-post-list/customer-post-list.component';
import {ForumTagListComponent} from './forum-tag-list/forum-tag-list.component';
import {ForumTagDetailComponent} from './forum-tag-detail/forum-tag-detail.component';
import {ForumActivityListComponent} from './forum-activity-list/forum-activity-list.component';
import {ForumActivityDetailComponent} from './forum-activity-detail/forum-activity-detail.component';
import {PostDetailComponent} from './post-detail/post-detail.component';

import {CusRichTextEditorModule} from '../forms/cus-rich-text-editor/cus-rich-text-editor.module';
import {ImagePickerModule} from '../image-picker/image-picker.module';
import {EcommerceCommonPipeSharedModule} from '../../ecommerce-common-pipe-shared.module';
import {ForumActivityRankComponent} from './forum-activity-rank/forum-activity-rank.component';
import {TopicStatisticsDialogComponent} from './topic-statistics-dialog/topic-statistics-dialog.component';
import {ForumCustomerCreateDialogComponent} from './forum-customer-create-dialog/forum-customer-create-dialog.component';
import {ForumCustomerListComponent} from './forum-customer-list/forum-customer-list.component';
import {PostAuditDialogComponent} from './post-audit-dialog/post-audit-dialog.component';
import {PostCommentListComponent} from './post-comment-list/post-comment-list.component';
import {PostCommentDetailComponent} from './post-comment-detail/post-comment-detail.component';
import {AfterSaleScreenshotDialogModule} from '../after-sale/after-sale-screenshot-dialog/after-sale-screenshot-dialog.module';
import {PromotionDialogModule} from '../promotion-dialog/promotion-dialog.module';

import {ForumStatusPipe} from './pipes';
// import {WishNoteComponent} from './wish-note.component';

const COMPS = [
  ChannelPostDetailComponent,
  ChannelPostListComponent,
  CustomerPostListComponent,
  ForumActivityDetailComponent,
  ForumActivityListComponent,
  ForumCustomerCreateDialogComponent,
  ForumCustomerListComponent,
  PostDetailComponent,
  ForumActivityRankComponent,
  ForumTagDetailComponent,
  ForumTagListComponent,
  TopicStatisticsDialogComponent,
  PostAuditDialogComponent,
  PostCommentListComponent,
  PostCommentDetailComponent,
];

@NgModule({
  imports: [
    AfterSaleScreenshotDialogModule,
    CommonModule,
    ComponentSharedModule,
    CusRichTextEditorModule,
    ImagePickerModule,
    EcommerceCommonPipeSharedModule,
    PromotionDialogModule,
  ],
  declarations: [ForumStatusPipe, ...COMPS],
  exports: [ForumStatusPipe, ...COMPS],
})
export class WishNoteModule {}
