/**
 * 报表模板类型定义
 */

/**
 * 字段类型
 */
export type FieldType = 'string' | 'number' | 'boolean' | 'date' | 'datetime';

/**
 * 过滤条件配置项
 */
export interface FilterConfigItem {
  field: string; // 字段名
  name: string; // 显示名称
  type: FieldType; // 字段类型
  defaultValue?: any; // 默认值
}

/**
 * 过滤条件配置
 */
export type FilterConfig = FilterConfigItem[];

/**
 * 表头配置项
 */
export interface HeaderConfigItem {
  field: string; // 字段名
  name: string; // 显示名称
  width?: number; // 列宽
  sortable?: boolean; // 是否可排序
}

/**
 * 表头配置
 */
export type HeaderConfig = HeaderConfigItem[];

/**
 * 过滤条件操作符
 */
export type FilterOperator = 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between';

/**
 * 过滤条件
 */
export interface ReportFilter {
  field: string; // 字段名
  operator: FilterOperator; // 操作符
  value: any; // 值
}

/**
 * 分页参数
 */
export interface ReportPagination {
  page: number; // 页码
  pageSize: number; // 每页记录数
}

/**
 * 报表查询输入
 */
export interface ReportQueryInput {
  templateId: string; // 模板ID
  filters?: ReportFilter[]; // 过滤条件
  pagination?: ReportPagination; // 分页参数
}

/**
 * 表头元数据
 */
export interface HeaderMeta {
  field: string; // 字段名
  name: string; // 显示名称
}

/**
 * 执行统计
 */
export interface ExecutionStats {
  executionTime: number; // 执行时间（毫秒）
  totalRows: number; // 总行数
}

/**
 * 报表查询结果
 */
export interface ReportQueryResult {
  headers: HeaderMeta[]; // 表头
  rows: any[]; // 数据行
  stats: ExecutionStats; // 统计信息
}
