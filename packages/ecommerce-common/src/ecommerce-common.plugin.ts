import {ForumPlugin} from '@scmally/forum';
import {KvsPlugin} from '@scmally/kvs';
import {
  GiftCardOrderEvent,
  GiftCardResolver,
  MemberOrderCancelEvent,
  MemberOrderEvent,
  MemberPlugin,
  MembershipPlanResolver,
} from '@scmally/member';
import {MinioPlugin} from '@scmally/minio';
import {RedLockPlugin} from '@scmally/red-lock';
import {VirtualCurrencyPlugin} from '@scmally/virtual-currency';
import {WeChatPlugin} from '@scmally/wechat';
import {
  ChannelEvent,
  CollectionEvent,
  Customer,
  CustomerEvent,
  EventBus,
  Fulfillment,
  ID,
  Logger,
  Order,
  OrderState,
  OrderStateTransitionEvent,
  PluginCommonModule,
  Product,
  ProductEvent,
  ProductVariantEvent,
  StockMovementEvent,
  VendurePlugin,
} from '@vendure/core';
import {ConfigurableOperationCodec} from '@vendure/core/dist/api/common/configurable-operation-codec';
import {IdCodecService} from '@vendure/core/dist/api/common/id-codec.service';
import {AdminUiExtension} from '@vendure/ui-devkit/compiler';
import path from 'path';
import {filter} from 'rxjs/operators';
import {
  ActivityCountdownAdminResolver,
  ActivityCountdownShopResolver,
  AnnouncementAdminResolver,
  AnnouncementResolver,
  AssistGiftAdminResolver,
  AssistGiftConfigAdminResolver,
  BannerAdminResolver,
  BannerResolver,
  BlindBoxActivityAdminResolver,
  BlindBoxActivityEntityResolver,
  BlindBoxActivityLimitConfigAdminResolver,
  BlindBoxActivityResolver,
  BlindBoxAdminResolver,
  BlindBoxBuyAdminResolver,
  BlindBoxBuyEntityResolver,
  BlindBoxEntityResolver,
  BlindBoxItemEntityResolver,
  BlindBoxOpenRecordEntityResolver,
  BlindBoxOpenRecordResolver,
  BlindBoxOrderResolver,
  BlindBoxStatisticsResolver,
  CommonAdminResolver,
  CommonResolver,
  CouponAdminResolver,
  CouponBundleAdminResolver,
  CouponBundleResolver,
  CouponResolver,
  CustomAssetResolver,
  CustomComponentResolver,
  CustomCustomerResolver,
  CustomOrderAdminResolver,
  CustomOrderLineResolver,
  CustomOrderResolver,
  CustomPageAdminResolver,
  CustomPageEntityResolver,
  CustomPageResolver,
  CustomProductAdminResolver,
  CustomProductEntityResolver,
  CustomProductOptionGroupResolver,
  CustomProductOptionResolver,
  CustomProductResolver,
  CustomProductVariantAdminResolver,
  CustomProductVariantEntityResolver,
  CustomProductVariantResolver,
  CustomShopProductResolver,
  CustomerSourceResolver,
  CustomerStatisticsAdminResolver,
  DiscountActivityAdminResolver,
  DiscountActivityResolver,
  DistributorAdminResolver,
  DistributorGroupAdminResolver,
  DistributorResolver,
  ExclusionGroupAdminResolver,
  ExclusionGroupResolver,
  ExclusionGroupShopResolver,
  ExportFileController,
  FirstCustomerBenefitAdminResolver,
  FirstCustomerBenefitResolver,
  FreeGiftAdminResolver,
  FullDiscountPresentAdminResolver,
  FullDiscountPresentResolver,
  GiftCardCommonOrderResolver,
  GiftCardCouponResolver,
  GiftCardOrderAdminResolver,
  GroupCustomerAdminResolver,
  HotWordAdminResolver,
  HotWordResolver,
  MemberCouponResolver,
  MemberPriceProductResolver,
  MemberPriceResolver,
  MembershipOrderResolver,
  MerchantVoluntaryRefundAdminResolver,
  MetricsResolver,
  OperationPlanAdminResolver,
  OrderPromotionResultResolver,
  OrderTrackingResolver,
  PackageDiscountAdminResolver,
  PackageDiscountResolver,
  ParticipatingActivitiesResolver,
  PaymentRewardActivityAdminResolver,
  PaymentRewardActivityResolver,
  PersonalCenterAdminResolver,
  PersonalCenterShopResolver,
  PointsAdminResolver,
  PointsConfigAdminResolver,
  PointsEntityResolver,
  PointsProductAdminResolver,
  PointsProductResolver,
  PointsResolver,
  ProductActivitiesResultResolver,
  ProductOptionGroupMasterResolver,
  ProductRestrictionsAdminResolver,
  ProductVariantResultResolver,
  PromotionAdminResolver,
  PromotionResultResolver,
  PurchasePremiumAdminResolver,
  PurchasePremiumResolver,
  ReviewProductAdminResolver,
  ReviewProductShopProductResolver,
  ReviewProductShopResolver,
  SelectiveGiftActivityAdminResolver,
  SelectiveGiftActivityResolver,
  SettingAdminResolver,
  SettingResolver,
  ShoppingCreditsClaimActivityAdminResolver,
  ShoppingCreditsClaimActivityShopResolver,
  ShoppingCreditsConfigAdminResolver,
  ShoppingCreditsConfigShopResolver,
  ShoppingCreditsDeductionActivityAdminResolver,
  ShoppingCreditsDeductionActivityShopResolver,
  UMengConfigAdminResolver,
  VirtualTargetResolver,
} from './api';
import {CheckinConfigAdminResolver} from './api/checkin-config-admin.resolver';
import {CheckinResolver} from './api/checkin-resolver';
import {ExportTaskAdminResolver} from './api/export-task-admin.resolver';
import {FloatingWindowAdminResolver, FloatingWindowShopResolver} from './api/floating-window.resolver';
import {ShareSettingAdminResolver, ShareSettingResolver} from './api/share-setting.resolver';
import {customFulfillmentProcess} from './config/process/fulfillment-process';
import {
  AssetCustomFields,
  ChannelCustomFields,
  CustomerCustomFields,
  OrderCustomFields,
  OrderLineCustomFields,
  ProductCustomFields,
  ProductOptionCustomFields,
  ProductOptionGroupCustomFields,
  ProductVariantCustomFields,
  PromotionCustomFields,
  ShippingMethodCustomFields,
} from './customFields';
import {
  Announcement,
  AssistGift,
  AssistGiftConfig,
  AssistGiftRecord,
  AssistRecord,
  Banner,
  BlindBox,
  BlindBoxActivity,
  BlindBoxActivityBooking,
  BlindBoxActivityBoxLink,
  BlindBoxActivityLimitConfig,
  BlindBoxBuy,
  BlindBoxItem,
  BlindBoxOpenRecord,
  BlindBoxOpenShoppingCart,
  BlindBoxOrderRecord,
  BlindBoxRefundRecord,
  Component,
  Coupon,
  CouponBundle,
  CouponBundleItem,
  CustomPage,
  CustomerOrderReferralSource,
  CustomerReferralSource,
  CustomerSource,
  CustomerSummary,
  DiscountActivity,
  Distributor,
  DistributorBinding,
  DistributorCustomer,
  DistributorDetail,
  DistributorGroup,
  DistributorGroupBinding,
  DistributorOrder,
  DistributorProductRecord,
  DistributorRecord,
  ErrorLogs,
  ExclusionGroup,
  ExclusionProduct,
  ExportTask,
  FirstCustomerBenefit,
  FirstCustomerBenefitItem,
  FirstCustomerBenefitRecord,
  FloatingWindow,
  FreeGift,
  FullDiscountPresent,
  GroupCustomer,
  HotWord,
  MemberPrice,
  MemberPriceProduct,
  MemberPriceProductVariant,
  MemberPromotionRecord,
  MerchantVoluntaryRefund,
  NanoDate,
  OperationPlan,
  OperationPlanCustomer,
  OrderLinePromotionDetail,
  OrderPaymentRewardCoupon,
  OrderPromotionResult,
  OrderTracking,
  OrderUploadShippingRecord,
  PackageDiscount,
  PaymentRewardActivity,
  PersonalCenter,
  PointsConfig,
  PointsHistory,
  PointsProduct,
  PointsProductSku,
  Popup,
  ProductOptionGroupMaster,
  ProductOptionMaster,
  ProductPromotionActive,
  ProductPurchasePermission,
  ProductRestrictions,
  PromotionResultDetail,
  PurchasePremium,
  PurchasePremiumProduct,
  ReportTemplate,
  ReviewProduct,
  SelectiveGiftActivity,
  Setting,
  ShareSetting,
  ShoppingCreditsClaimActivity,
  ShoppingCreditsConfig,
  ShoppingCreditsDeductionActivity,
  SystemDailyStats,
  SystemHourlyStats,
  TemplateConfig,
  UMengConfig,
  UserCoupon,
  VirtualDeliveryRecord,
  WechatLogisticsCompany,
} from './entities';
import {ActivityCountdown} from './entities/activity-countdown.entity';
import {CheckinConfig} from './entities/checkin-config.entity';
import {CheckinCycle} from './entities/checkin-cycle.entity';
import {CheckinPrizeDay} from './entities/checkin-prize-day.entity';
import {CheckinPrize} from './entities/checkin-prize.entity';
import {CustomerBindingDistributor} from './entities/customer-binding-distributor.entity';
import {CustomerCheckinRecords} from './entities/customer-checkin-records.entity';
import {CustomerConsecutiveCheckin} from './entities/customer-consecutive-checkin.entity';
import {CustomerPoints} from './entities/customer-points.entity';
import {CustomerPrizeRecords} from './entities/customer-prize-records.entity';
import {BlindBoxOrderEvent, MerchantVoluntaryRefundEvent} from './event';
import {adminSchemaExtensions, shopSchemaExtensions} from './graphql-schemas';
import {
  ActivityCountdownOperate,
  AnnouncementOperate,
  AssistGiftOperate,
  BannerOperate,
  BlindBoxActivityLimitConfigOperate,
  BlindBoxActivityOperate,
  BlindBoxBuyOperate,
  BlindBoxStatisticsOperate,
  CheckinConfigOperate,
  CouponBundleOperate,
  CouponFailureOperate,
  CouponOperate,
  CouponPermission,
  CustomPageOperate,
  CustomerDataExportFile,
  CustomerDistributionBindingExport,
  DiscountActivityOperate,
  DistributorOperate,
  DistributorPermission,
  ExclusionGroupOperate,
  ExportTaskOperate,
  FirstCustomerBenefitOperate,
  FloatingWindowOperate,
  FreeGiftOperate,
  FullDiscountPresentOperate,
  GiftCardOrderPermission,
  GroupCustomerOperate,
  HotWordOperate,
  MemberOrderDataExport,
  MemberPricePermission,
  MerchantVoluntaryRefundPermission,
  OperationPlanOperate,
  PackageDiscountOperate,
  PaymentRewardActivityPermission,
  PersonalCenterOperate,
  PointsConfigOperate,
  PointsProductOperate,
  ProductDataStatisticsExportFile,
  ProductRestrictionsOperate,
  PurchasePremiumOperate,
  SelectiveGiftActivityOperate,
  SetCustomerDistributorPermission,
  SettingOperate,
  ShareOperate,
  ShareSettingPermission,
  ShoppingCreditsClaimActivityOperate,
  ShoppingCreditsConfigOperate,
  ShoppingCreditsDeductionActivityOperate,
  StatisticsQuery,
  TransactionCustomerStatisticsPermission,
  UMengConfigPermission,
  UserCouponPermission,
} from './permission-definition';
import {
  automaticAction,
  circulationDiscount,
  circulationPresent,
  convertibilityAction,
  ladderDiscount,
  ladderPresent,
  orderDiscountMax,
  orderFixedAmount,
  packageDiscountAction,
  productsDiscountNext,
  purchasePremiumAction,
} from './promotion/action';
import {AutomaticActionService} from './promotion/automatic.action.service';
import {
  automaticConditions,
  customerGroupList,
  packageDiscountConditions,
  productConvertibility,
  productQuantityContain,
  productTotalPriceConditions,
} from './promotion/conditions';
import {
  ActivityCountdownService,
  AnnouncementService,
  AssistGiftConfigService,
  AssistGiftService,
  BannerService,
  BlindBoxActivityLimitConfigService,
  BlindBoxActivityService,
  BlindBoxOpenRecordService,
  BlindBoxOrderService,
  BlindBoxService,
  BlindBoxStatisticsService,
  CacheService,
  CheckinConfigService,
  CommonService,
  CouponBundleService,
  CouponService,
  CrontabService,
  CustomAssetService,
  CustomPageService,
  CustomProductOptionGroupService,
  CustomPromotionService,
  CustomerOrderService,
  CustomerProductService,
  CustomerProductVariantService,
  CustomerSourceService,
  CustomerStatisticsService,
  CustomerSummaryService,
  DiscountActivityService,
  DistributorGroupService,
  DistributorService,
  ExclusionGroupService,
  ExportFileService,
  ExportTaskService,
  FirstCustomerBenefitService,
  FloatingWindowService,
  FreeGiftService,
  FullDiscountPresentService,
  GiftCardCommonOrderService,
  GroupCustomerService,
  HotWordService,
  MemberPriceService,
  MerchantVoluntaryRefundService,
  OperationPlanService,
  OrderCustomCommonService,
  OrderPromotionResultService,
  OrderTrackingService,
  PackageDiscountService,
  PayCouponService,
  PaymentRewardActivityService,
  PersonalCenterService,
  PointsConfigService,
  PointsProductService,
  PointsService,
  ProductCustomService,
  ProductOptionGroupMasterService,
  ProductPromotionActiveService,
  ProductRestrictionsService,
  ProductVariantDailyStatsService,
  PromotionActivityService,
  PromotionDetailProductService,
  PromotionExtendService,
  PromotionResultDetailService,
  ProvablyFairService,
  PurchasePremiumService,
  ReviewProductService,
  SelectiveGiftActivityService,
  SendMessageService,
  SettingService,
  ShoppingCreditsClaimActivityService,
  ShoppingCreditsConfigService,
  ShoppingCreditsDeductionActivityService,
  UMengConfigService,
  WechatLogisticsCompanyService,
  customOrderProcess,
} from './service';
import {CheckinService} from './service/checkin.service';
import {LocaleStringHydrator} from './service/custom-locale-string-hydrator';
import {CustomStockMovementService} from './service/custom-stock-movement.service';
import {CustomerPointsService} from './service/customer-points.service';
import {MatomoService} from './service/matomo.service';
import {ShareSettingService} from './service/share-setting.service';
import {freightChargeChecker, productFreeShippingChecker, productShippingChecker} from './shipping-method';
import {blindBoxShippingChecker} from './shipping-method/blind-box-shipping-checker';
@VendurePlugin({
  imports: [
    PluginCommonModule,
    KvsPlugin,
    WeChatPlugin,
    MinioPlugin,
    MemberPlugin,
    ForumPlugin,
    RedLockPlugin,
    VirtualCurrencyPlugin,
  ],
  entities: [
    Banner,
    ReviewProduct,
    Component,
    CustomPage,
    HotWord,
    Announcement,
    Coupon,
    UserCoupon,
    PurchasePremium,
    PurchasePremiumProduct,
    Distributor,
    DistributorBinding,
    DistributorOrder,
    DiscountActivity,
    PointsHistory,
    FreeGift,
    FullDiscountPresent,
    ProductPromotionActive,
    Setting,
    OrderPromotionResult,
    MemberPromotionRecord,
    MerchantVoluntaryRefund,
    PromotionResultDetail,
    OrderLinePromotionDetail,
    SystemDailyStats,
    SystemHourlyStats,
    GroupCustomer,
    OperationPlan,
    OperationPlanCustomer,
    WechatLogisticsCompany,
    PackageDiscount,
    DistributorDetail,
    UMengConfig,
    OrderTracking,
    DistributorGroup,
    DistributorGroupBinding,
    MemberPrice,
    MemberPriceProduct,
    MemberPriceProductVariant,
    NanoDate,
    DistributorCustomer,
    DistributorProductRecord,
    ProductOptionGroupMaster,
    ProductOptionMaster,
    OrderUploadShippingRecord,
    VirtualDeliveryRecord,
    SelectiveGiftActivity,
    PaymentRewardActivity,
    OrderPaymentRewardCoupon,
    ErrorLogs,
    DistributorRecord,
    CouponBundle,
    CouponBundleItem,
    ProductPurchasePermission,
    FirstCustomerBenefit,
    FirstCustomerBenefitItem,
    FirstCustomerBenefitRecord,
    ExclusionProduct,
    ExclusionGroup,
    PointsProduct,
    PointsProductSku,
    PointsConfig,
    ProductRestrictions,
    CheckinConfig,
    CheckinCycle,
    CheckinPrizeDay,
    CheckinPrize,
    CustomerCheckinRecords,
    CustomerPrizeRecords,
    CustomerConsecutiveCheckin,
    Popup,
    CustomerPoints,
    CustomerBindingDistributor,
    AssistGiftConfig,
    AssistGiftRecord,
    AssistGift,
    AssistRecord,
    BlindBoxActivityBoxLink,
    BlindBoxActivityLimitConfig,
    BlindBoxActivity,
    BlindBoxBuy,
    BlindBoxItem,
    BlindBoxOpenRecord,
    BlindBoxOrderRecord,
    BlindBox,
    ExportTask,
    TemplateConfig,
    CustomerSource,
    BlindBoxOpenShoppingCart,
    BlindBoxRefundRecord,
    BlindBoxActivityBooking,
    FloatingWindow,
    CustomerReferralSource,
    CustomerOrderReferralSource,
    ShareSetting,
    ShoppingCreditsClaimActivity,
    ShoppingCreditsDeductionActivity,
    ShoppingCreditsConfig,
    PersonalCenter,
    ActivityCountdown,
    CustomerSummary,
    ReportTemplate,
  ],
  providers: [
    CommonService,
    BannerService,
    ReviewProductService,
    CrontabService,
    CustomPageService,
    HotWordService,
    AnnouncementService,
    SendMessageService,
    CouponService,
    ProductPromotionActiveService,
    PurchasePremiumService,
    DistributorService,
    ProductCustomService,
    PointsService,
    DiscountActivityService,
    FreeGiftService,
    FullDiscountPresentService,
    PromotionActivityService,
    SettingService,
    PromotionExtendService,
    OrderPromotionResultService,
    MerchantVoluntaryRefundService,
    OrderCustomCommonService,
    PromotionResultDetailService,
    GroupCustomerService,
    OperationPlanService,
    PromotionDetailProductService,
    WechatLogisticsCompanyService,
    ProductActivitiesResultResolver,
    PackageDiscountService,
    CustomerStatisticsService,
    ProductVariantDailyStatsService,
    UMengConfigService,
    OrderTrackingService,
    DistributorGroupService,
    MatomoService,
    MemberPriceService,
    GiftCardCommonOrderService,
    ExportFileService,
    CustomAssetService,
    ProductOptionGroupMasterService,
    CustomerProductVariantService,
    CustomerProductService,
    CustomerOrderService,
    SelectiveGiftActivityService,
    PaymentRewardActivityService,
    PayCouponService,
    ConfigurableOperationCodec,
    IdCodecService,
    CouponBundleService,
    FirstCustomerBenefitService,
    CacheService,
    CustomProductOptionGroupService,
    LocaleStringHydrator,
    AutomaticActionService,
    ExclusionGroupService,
    CustomStockMovementService,
    PointsProductService,
    PointsConfigService,
    ProductRestrictionsService,
    CheckinConfigService,
    CheckinService,
    ExportTaskService,
    CustomerPointsService,
    BlindBoxService,
    BlindBoxActivityService,
    BlindBoxActivityLimitConfigService,
    AssistGiftService,
    AssistGiftConfigService,
    BlindBoxOrderService,
    CustomerSourceService,
    BlindBoxStatisticsService,
    BlindBoxOpenRecordService,
    FloatingWindowService,
    ProvablyFairService,
    ShareSettingService,
    CustomPromotionService,
    ShoppingCreditsClaimActivityService,
    ShoppingCreditsDeductionActivityService,
    ShoppingCreditsConfigService,
    PersonalCenterService,
    ActivityCountdownService,
    CustomerSummaryService,
    ReportTemplateService,
  ],
  shopApiExtensions: {
    schema: shopSchemaExtensions,
    resolvers: [
      HotWordResolver,
      BannerResolver,
      ReviewProductShopResolver,
      ReviewProductShopProductResolver,
      AnnouncementResolver,
      CustomPageResolver,
      CouponResolver,
      PurchasePremiumResolver,
      DistributorResolver,
      MemberCouponResolver,
      ParticipatingActivitiesResolver,
      SettingResolver,
      ProductActivitiesResultResolver,
      DiscountActivityResolver,
      FullDiscountPresentResolver,
      PromotionResultResolver,
      CustomOrderLineResolver,
      CustomOrderResolver,
      CustomProductVariantResolver,
      CommonResolver,
      PackageDiscountResolver,
      OrderTrackingResolver,
      MemberPriceProductResolver,
      GiftCardCommonOrderResolver,
      GiftCardCouponResolver,
      CustomCustomerResolver,
      CustomAssetResolver,
      MembershipPlanResolver,
      GiftCardResolver,
      CustomComponentResolver,
      CustomProductResolver,
      CustomProductEntityResolver,
      CustomProductVariantEntityResolver,
      SelectiveGiftActivityResolver,
      OrderPromotionResultResolver,
      PaymentRewardActivityResolver,
      VirtualTargetResolver,
      ProductVariantResultResolver,
      CouponBundleResolver,
      FirstCustomerBenefitResolver,
      CustomProductOptionResolver,
      CustomProductOptionGroupResolver,
      CustomShopProductResolver,
      ExclusionGroupResolver,
      ExclusionGroupShopResolver,
      PointsProductResolver,
      PointsResolver,
      PointsEntityResolver,
      CheckinResolver,
      CustomPageEntityResolver,
      BlindBoxOrderResolver,
      BlindBoxActivityResolver,
      BlindBoxActivityEntityResolver,
      BlindBoxBuyEntityResolver,
      BlindBoxEntityResolver,
      BlindBoxItemEntityResolver,
      CustomerSourceResolver,
      BlindBoxOpenRecordResolver,
      FloatingWindowShopResolver,
      BlindBoxOpenRecordEntityResolver,
      ShareSettingResolver,
      ShoppingCreditsClaimActivityShopResolver,
      ShoppingCreditsDeductionActivityShopResolver,
      ShoppingCreditsConfigShopResolver,
      PersonalCenterShopResolver,
      ActivityCountdownShopResolver,
    ],
  },
  adminApiExtensions: {
    schema: adminSchemaExtensions,
    resolvers: [
      AnnouncementAdminResolver,
      HotWordAdminResolver,
      BannerAdminResolver,
      ReviewProductAdminResolver,
      CustomPageAdminResolver,
      CouponAdminResolver,
      PurchasePremiumAdminResolver,
      CommonAdminResolver,
      DistributorAdminResolver,
      DiscountActivityAdminResolver,
      FreeGiftAdminResolver,
      FullDiscountPresentAdminResolver,
      SettingAdminResolver,
      PromotionAdminResolver,
      MerchantVoluntaryRefundAdminResolver,
      CustomOrderLineResolver,
      CustomOrderResolver,
      GroupCustomerAdminResolver,
      OperationPlanAdminResolver,
      PackageDiscountAdminResolver,
      CustomerStatisticsAdminResolver,
      UMengConfigAdminResolver,
      DistributorGroupAdminResolver,
      MemberPriceResolver,
      GiftCardOrderAdminResolver,
      GiftCardCouponResolver,
      ProductOptionGroupMasterResolver,
      CustomProductVariantAdminResolver,
      CustomProductEntityResolver,
      CustomProductVariantEntityResolver,
      CustomOrderAdminResolver,
      SelectiveGiftActivityAdminResolver,
      OrderPromotionResultResolver,
      MetricsResolver,
      PaymentRewardActivityAdminResolver,
      VirtualTargetResolver,
      ProductVariantResultResolver,
      CustomProductAdminResolver,
      CustomProductOptionResolver,
      CustomProductOptionGroupResolver,
      MembershipOrderResolver,
      CouponBundleAdminResolver,
      FirstCustomerBenefitAdminResolver,
      ExclusionGroupAdminResolver,
      ExclusionGroupResolver,
      PointsProductAdminResolver,
      PointsConfigAdminResolver,
      PointsEntityResolver,
      ProductRestrictionsAdminResolver,
      CheckinConfigAdminResolver,
      CustomPageEntityResolver,
      ExportTaskAdminResolver,
      CustomCustomerResolver,
      BlindBoxAdminResolver,
      BlindBoxActivityAdminResolver,
      BlindBoxActivityLimitConfigAdminResolver,
      AssistGiftAdminResolver,
      AssistGiftConfigAdminResolver,
      BlindBoxActivityEntityResolver,
      BlindBoxBuyEntityResolver,
      BlindBoxEntityResolver,
      BlindBoxItemEntityResolver,
      BlindBoxBuyAdminResolver,
      BlindBoxStatisticsResolver,
      FloatingWindowAdminResolver,
      PointsAdminResolver,
      ShareSettingAdminResolver,
      ShoppingCreditsClaimActivityAdminResolver,
      ShoppingCreditsDeductionActivityAdminResolver,
      ShoppingCreditsConfigAdminResolver,
      PersonalCenterAdminResolver,
      ActivityCountdownAdminResolver,
      ReportTemplateResolver,
    ],
  },
  controllers: [ExportFileController],
  configuration: config => {
    config.authOptions.customPermissions.push(BannerOperate);
    config.authOptions.customPermissions.push(CustomPageOperate);
    config.authOptions.customPermissions.push(HotWordOperate);
    config.authOptions.customPermissions.push(AnnouncementOperate);
    config.authOptions.customPermissions.push(CouponOperate);
    config.authOptions.customPermissions.push(CouponPermission); //优惠券权限
    config.authOptions.customPermissions.push(CouponFailureOperate); //优惠券失效权限
    config.authOptions.customPermissions.push(UserCouponPermission); //用户优惠券权限
    config.authOptions.customPermissions.push(MerchantVoluntaryRefundPermission); //商户自主退款权限
    config.authOptions.customPermissions.push(ShareOperate); //分享推广权限
    config.authOptions.customPermissions.push(PurchasePremiumOperate);
    config.authOptions.customPermissions.push(DistributorOperate);
    config.authOptions.customPermissions.push(DistributorPermission);
    config.authOptions.customPermissions.push(SetCustomerDistributorPermission);
    config.authOptions.customPermissions.push(DiscountActivityOperate);
    config.authOptions.customPermissions.push(FullDiscountPresentOperate);
    config.authOptions.customPermissions.push(FreeGiftOperate);
    config.authOptions.customPermissions.push(SettingOperate);
    config.authOptions.customPermissions.push(StatisticsQuery);
    config.authOptions.customPermissions.push(GroupCustomerOperate);
    config.authOptions.customPermissions.push(OperationPlanOperate);
    config.authOptions.customPermissions.push(PackageDiscountOperate);
    config.authOptions.customPermissions.push(TransactionCustomerStatisticsPermission);
    config.authOptions.customPermissions.push(UMengConfigPermission);
    config.authOptions.customPermissions.push(MemberPricePermission);
    config.authOptions.customPermissions.push(GiftCardOrderPermission);
    config.authOptions.customPermissions.push(SelectiveGiftActivityOperate);
    config.authOptions.customPermissions.push(PaymentRewardActivityPermission);
    config.authOptions.customPermissions.push(ProductDataStatisticsExportFile);
    config.authOptions.customPermissions.push(CustomerDataExportFile);
    config.authOptions.customPermissions.push(MemberOrderDataExport);
    config.authOptions.customPermissions.push(CustomerDistributionBindingExport);
    config.authOptions.customPermissions.push(CouponBundleOperate);
    config.authOptions.customPermissions.push(FirstCustomerBenefitOperate);
    config.authOptions.customPermissions.push(ExclusionGroupOperate);
    config.authOptions.customPermissions.push(PointsProductOperate);
    config.authOptions.customPermissions.push(PointsConfigOperate);
    config.authOptions.customPermissions.push(ProductRestrictionsOperate);
    config.authOptions.customPermissions.push(CheckinConfigOperate);
    config.authOptions.customPermissions.push(ExportTaskOperate);
    config.authOptions.customPermissions.push(BlindBoxActivityOperate);
    config.authOptions.customPermissions.push(BlindBoxActivityLimitConfigOperate);
    config.authOptions.customPermissions.push(AssistGiftOperate);
    config.authOptions.customPermissions.push(BlindBoxBuyOperate);
    config.authOptions.customPermissions.push(BlindBoxStatisticsOperate);
    config.authOptions.customPermissions.push(FloatingWindowOperate);
    config.authOptions.customPermissions.push(ShareSettingPermission);
    config.authOptions.customPermissions.push(ShoppingCreditsClaimActivityOperate);
    config.authOptions.customPermissions.push(ShoppingCreditsDeductionActivityOperate);
    config.authOptions.customPermissions.push(ShoppingCreditsConfigOperate);
    config.authOptions.customPermissions.push(PersonalCenterOperate);
    config.authOptions.customPermissions.push(ActivityCountdownOperate);
    config.authOptions.customPermissions.push(CreateReportTemplatePermission);
    config.authOptions.customPermissions.push(ReadReportTemplatePermission);
    config.authOptions.customPermissions.push(UpdateReportTemplatePermission);
    config.authOptions.customPermissions.push(DeleteReportTemplatePermission);
    config.authOptions.customPermissions.push(ExecuteReportPermission);
    config.orderOptions.process.push(customOrderProcess);
    config.shippingOptions.process = [customFulfillmentProcess];
    config.promotionOptions.promotionConditions.push(productTotalPriceConditions);
    config.promotionOptions.promotionConditions.push(productConvertibility);
    config.promotionOptions.promotionConditions.push(productQuantityContain);
    config.promotionOptions.promotionConditions.push(customerGroupList);
    config.promotionOptions.promotionConditions.push(automaticConditions);
    config.promotionOptions.promotionConditions.push(packageDiscountConditions);
    config.promotionOptions.promotionActions.push(orderDiscountMax);
    config.promotionOptions.promotionActions.push(purchasePremiumAction);
    config.promotionOptions.promotionActions.push(convertibilityAction);
    config.promotionOptions.promotionActions.push(productsDiscountNext);
    config.promotionOptions.promotionActions.push(circulationDiscount);
    config.promotionOptions.promotionActions.push(circulationPresent);
    config.promotionOptions.promotionActions.push(ladderDiscount);
    config.promotionOptions.promotionActions.push(ladderPresent);
    config.promotionOptions.promotionActions.push(orderFixedAmount);
    config.promotionOptions.promotionActions.push(automaticAction);
    config.promotionOptions.promotionActions.push(packageDiscountAction);
    config.shippingOptions.shippingEligibilityCheckers.push(freightChargeChecker);
    config.shippingOptions.shippingEligibilityCheckers.push(productFreeShippingChecker);
    config.shippingOptions.shippingEligibilityCheckers.push(productShippingChecker);
    config.shippingOptions.shippingEligibilityCheckers.push(blindBoxShippingChecker);
    config.customFields.ShippingMethod.push(...ShippingMethodCustomFields);
    config.customFields.Asset.push(...AssetCustomFields);
    config.customFields.OrderLine.push(...OrderLineCustomFields);
    config.customFields.Customer.push(...CustomerCustomFields);
    config.customFields.Product.push(...ProductCustomFields);
    config.customFields.Channel.push(...ChannelCustomFields);
    config.customFields.Promotion.push(...PromotionCustomFields);
    config.customFields.ProductVariant.push(...ProductVariantCustomFields);
    config.customFields.Order.push(...OrderCustomFields);
    config.customFields.ProductOptionGroup.push(...ProductOptionGroupCustomFields);
    config.customFields.ProductOption.push(...ProductOptionCustomFields);
    config.entityOptions.metadataModifiers = [
      metadata => {
        // const instance = new Order();
        // Index()(instance, 'state');
        const orderEntityMetadata = metadata.tables.find(e => e.target === Order);
        if (orderEntityMetadata) {
          metadata.indices.push({
            target: Order,
            columns: ['state'],
            unique: false,
          });
          // 添加索引，字段 'active'
          metadata.indices.push({
            target: Order,
            columns: ['active'],
            unique: false,
          });
          // 添加索引，字段 'orderPlacedAt'
          metadata.indices.push({
            target: Order,
            columns: ['orderPlacedAt'],
            unique: false,
          });
          // 添加索引，字段 'code'
          // metadata.indices.push({
          //   target: Order,
          //   columns: ['code'],
          //   unique: false,
          // });
          // 添加复合索引，字段 state 和 active 以及 orderPlacedAt
          metadata.indices.push({
            target: Order,
            columns: ['state', 'active', 'orderPlacedAt'],
            unique: false,
          });
          // 添加索引，字段 'customFieldsTimeoutperiodtobepaid'
          metadata.indices.push({
            target: Order,
            columns: ['customFields.timeoutPeriodToBePaid'],
            unique: false,
          });

          // 添加索引，字段 'customFieldsTimeoutperiodtobereceived'
          metadata.indices.push({
            target: Order,
            columns: ['customFields.timeoutPeriodToBeReceived'],
            unique: false,
          });

          // 添加索引，字段 'customFieldsConfirmreceipttime'
          metadata.indices.push({
            target: Order,
            columns: ['customFields.confirmReceiptTime'],
            unique: false,
          });
          // 添加索引，字段 'customFieldsReceivername'
          metadata.indices.push({
            target: Order,
            columns: ['customFields.receiverName'],
            unique: false,
          });
          // 添加索引，字段 'customFieldsReceiverphone'
          metadata.indices.push({
            target: Order,
            columns: ['customFields.receiverPhone'],
            unique: false,
          });
        }
        const fulfillmentsEntityMetadata = metadata.tables.find(e => e.target === Fulfillment);
        if (fulfillmentsEntityMetadata) {
          metadata.indices.push({
            target: Fulfillment,
            columns: ['state'],
            unique: false,
          });
          metadata.indices.push({
            target: Fulfillment,
            columns: ['createdAt'],
            unique: false,
          });
        }
        const customerEntityMetadata = metadata.tables.find(e => e.target === Customer);
        if (customerEntityMetadata) {
          metadata.indices.push({
            target: Customer,
            columns: ['deletedAt'],
            unique: false,
          });
          metadata.indices.push({
            target: Customer,
            columns: ['createdAt'],
            unique: false,
          });
          metadata.indices.push({
            target: Customer,
            columns: ['phoneNumber'],
            unique: false,
          });
        }
        const distributorBindingEntityMetadata = metadata.tables.find(e => e.target === DistributorBinding);
        if (distributorBindingEntityMetadata) {
          metadata.indices.push({
            target: DistributorBinding,
            columns: ['createdAt'],
            unique: false,
          });
          metadata.indices.push({
            target: DistributorBinding,
            columns: ['updatedAt'],
            unique: false,
          });
        }
        const productEntityMetadata = metadata.tables.find(e => e.target === Product);
        if (productEntityMetadata) {
          metadata.indices.push({
            target: Product,
            columns: ['customFields.productType'],
            unique: false,
          });
          metadata.indices.push({
            target: Product,
            columns: ['customFields.virtualTargetType'],
            unique: false,
          });
        }
      },
    ];
    return config;
  },
})
export class CommonPlugin {
  constructor(
    private eventBus: EventBus,
    private distributorService: DistributorService,
    private couponService: CouponService,
    private pointsService: PointsService,
    private productCustomService: ProductCustomService,
    private orderPromotionResultService: OrderPromotionResultService,
    private wechatLogisticsCompanyService: WechatLogisticsCompanyService,
    private promotionExtendService: PromotionExtendService,
    private productVariantDailyStatsService: ProductVariantDailyStatsService,
    private commonService: CommonService,
    private orderTrackingService: OrderTrackingService,
    private matomoService: MatomoService,
    private crontabService: CrontabService,
    private paymentRewardActivityService: PaymentRewardActivityService,
    private orderCustomCommonService: OrderCustomCommonService,
    private payCouponService: PayCouponService,
    private cacheService: CacheService,
    private customerProductVariantService: CustomerProductVariantService,
    private customerProductService: CustomerProductService,
    private freeGiftService: FreeGiftService,
    private blindBoxOrderService: BlindBoxOrderService,
    private customerSourceService: CustomerSourceService,
    private shoppingCreditsClaimActivityService: ShoppingCreditsClaimActivityService,
    private shoppingCreditsDeductionActivityService: ShoppingCreditsDeductionActivityService,
  ) {}
  // 是否禁用定时器
  static isDisableCron = false;
  static init(option: {isDisableCron: boolean}) {
    this.isDisableCron = option.isDisableCron ?? false;
    return this;
  }

  onModuleInit() {
    this.crontabService.init(CommonPlugin.isDisableCron);
  }

  // TODO 去除自定义缓存  2024-08-30
  // 加载缓存middleware
  // configure(consumer: MiddlewareConsumer) {
  // consumer.apply(CustomBorderParserMiddleware).forRoutes({
  //   path: '/shop-api',
  //   method: RequestMethod.POST,
  // });
  // consumer.apply(CustomBorderParserMiddleware).forRoutes({
  //   path: '/admin-api',
  //   method: RequestMethod.POST,
  // });
  // }
  onApplicationBootstrap() {
    this.eventBus
      .ofType(BlindBoxOrderEvent)
      .pipe(filter(event => event.type === 'PaymentSettled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.distributorService.addDistributorToBlindBoxOrder(event.ctx, event.blindBoxBuy.id);
        } catch (error) {
          Logger.error(`更新盲盒购买的订单错误:${error}`);
        }
        // 支付完成修改盲盒活动的销量和库存
        try {
          await this.blindBoxOrderService.paymentSettledUpdateSalesAndStock(event.ctx, event.blindBoxBuy.id);
        } catch (error) {
          Logger.error(`盲盒支付完成修改盲盒活动的销量和库存:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'ArrangingPayment'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          // 根据用户最新来源保存订单来源
          await this.customerSourceService.saveOrderSource(event.ctx, event.order);
        } catch (error) {
          Logger.error(`会员优惠结果保存错误:${error}`);
        }
      });

    this.eventBus
      .ofType(BlindBoxOrderEvent)
      .pipe(filter(event => event.type === 'Cancelled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.distributorService.cancelBlindBoxOrderDistributorDetail(event.ctx, event.blindBoxBuy.id);
        } catch (error) {
          Logger.error(`取消盲盒购买的订单错误:${error}`);
        }
        // 盲盒订单取消 后更新盲盒活动的销量和库存
        try {
          await this.blindBoxOrderService.cancelUpdateSalesAndStock(event.ctx, event.blindBoxBuy.id);
        } catch (error) {
          Logger.error(`盲盒订单取消后更新盲盒活动的销量和库存:${error}`);
        }
      });

    this.eventBus
      .ofType(MemberOrderEvent)
      .pipe(filter(event => event.type === 'PaymentSettled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.distributorService.addDistributorToMemberOrder(event.ctx, event.membershipOrder?.id);
        } catch (error) {
          Logger.error(`更新分销员下会员卡购买的订单错误:${error}`);
        }
      });

    this.eventBus
      .ofType(GiftCardOrderEvent)
      // .pipe(filter(event => event.type === 'PaymentSettled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        if (event.type === 'PaymentSettled') {
          try {
            await this.distributorService.addDistributorToGiftCardOrder(event.ctx, event.giftCardOrder?.id);
          } catch (error) {
            Logger.error(`更新分销员下礼品卡购买的订单错误:${error}`);
          }
        } else if (event.type === 'Cancelled' && event.price > 0) {
          try {
            await this.distributorService.cancelGiftCardOrderDistributorDetail(
              event.ctx,
              event.giftCardOrder?.id,
              event.price,
            );
          } catch (error) {
            Logger.error(`取消分销员下礼品卡购买的订单错误:${error}`);
          }
        }
      });

    this.eventBus
      .ofType(MemberOrderCancelEvent)
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          const memberOrders = event.memberOrderCancels;
          if (memberOrders.length > 0) {
            for (const memberOrder of memberOrders) {
              const result = await this.distributorService.cancelMemberOrderDistributorDetail(
                event.ctx,
                memberOrder.memberOrderId,
                memberOrder.price,
              );
              Logger.info(`会员订单${memberOrder},result:${result}`);
            }
          }
        } catch (error) {
          Logger.error(`更新分销员下会员卡购买的订单错误:${error}`);
        }

        // 全部会员卡订单取消
        try {
          const memberOrders = event.memberOrderCancels;
          if (memberOrders.length > 0) {
            for (const memberOrder of memberOrders) {
              await this.pointsService.memberOrderCancel(event.ctx, memberOrder);
            }
          }
        } catch (error) {
          Logger.error(`会员卡全部订单取消退回积分错误:${error}`);
        }
      });
    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === ('ConfirmReceiptOfGoods' as OrderState)))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.pointsService.confirmReceiptOfGoodsBonusPoints(event.ctx, event.order);
        } catch (error) {
          Logger.error(`确认收货积分奖励错误:${error}`);
        }
        try {
          // 确认收货后发放购物金
          await this.shoppingCreditsClaimActivityService.deliveryShoppingCreditsClaimActivity(event.ctx, event.order);
        } catch (error) {
          Logger.error(`确认收货处理购物金错误:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'PaymentSettled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        // 支付完成赠品库存修改
        try {
          await this.freeGiftService.paymentSettledGiftStock(event.ctx, event.order);
        } catch (error) {
          Logger.error(`支付完成赠品库存修改错误:${error}`);
        }

        try {
          //添加分销员订单
          await this.distributorService.addDistributorToOrder(event.ctx, event.order);
        } catch (error) {
          Logger.error(`添加分销员订单错误:${error}`);
        }
        try {
          //优惠券使用
          await this.couponService.couponUse(event.ctx, event.order);
        } catch (error) {
          Logger.error(`优惠券使用错误:${error}`);
        }
        try {
          //积分奖励
          await this.pointsService.paymentSettledBonusPoints(event.ctx, event.order);
        } catch (error) {
          Logger.error(`积分奖励错误:${error}`);
        }
        // try {
        //   // 支付有礼
        //   await this.paymentRewardActivityService.paymentSettled(event.ctx, event.order);
        // } catch (error) {
        //   Logger.error(`支付有礼奖励错误:${error}`);
        // }
        try {
          //产品销量添加
          await this.productCustomService.salesVolumeAdd(event.ctx, event.order);
        } catch (error) {
          Logger.error(`产品销量添加错误:${error}`);
        }
        try {
          // 优惠结果明细
          await this.orderPromotionResultService.createOrderPromotionResultDetail(event.ctx, event.order);
        } catch (error) {
          Logger.error(`优惠结果明细错误:${error}`);
        }
        try {
          //支付完成修改订单追踪信息
          await this.orderTrackingService.updateOrderTracking(event.ctx, event.order.id);
        } catch (error) {
          Logger.error(`支付完成订单追踪信息错误:${error}`);
        }
        try {
          // 支付完成判断是否是虚拟商品,如果是虚拟商品,则自动发货,并且根据虚拟商品的类型进行不同的处理
          await this.orderCustomCommonService.virtualProductAutoDelivery(event.ctx, event.order.id);
        } catch (error) {
          Logger.error(`虚拟商品自动发货错误:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .pipe(filter(event => (event.toState as any) === 'Delivered'))
      // 支付有礼优惠券赠送修改为物流收货后赠送
      // .pipe(filter(event => (event.toState as any) === 'ConfirmReceiptOfGoods'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          // 支付有礼
          await this.paymentRewardActivityService.paymentSettled(event.ctx, event.order);
        } catch (error) {
          Logger.error(`支付有礼奖励错误:${error}`);
        }

        try {
          // 物流收货后发放购物金
          await this.shoppingCreditsClaimActivityService.deliveryShoppingCreditsClaimActivity(event.ctx, event.order);
        } catch (error) {
          Logger.error(`支付完成处理购物金错误:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'PaymentSettled'))
      // 支付完成出了购物金逻辑
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.shoppingCreditsClaimActivityService.paymentSettledShoppingCreditsClaimActivity(
            event.ctx,
            event.order,
          );
        } catch (error) {
          Logger.error(`支付完成处理购物金错误:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'ArrangingPayment'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.orderPromotionResultService.memberPromotionResult(event.ctx, event.order);
        } catch (error) {
          Logger.error(`会员优惠结果保存错误:${error}`);
        }
        try {
          await this.orderPromotionResultService.productLimitationResult(event.ctx, event.order);
        } catch (error) {
          Logger.error(`产品限购结果保存错误:${error}`);
        }
        try {
          //保存订单追踪信息
          await this.orderTrackingService.saveOrderTracking(event.ctx, event.order);
        } catch (error) {
          Logger.error(`保存订单追踪信息错误:${error}`);
        }
        // 保存用户订单是否会员
        try {
          await this.orderPromotionResultService.saveCustomerOrderIsMember(event.ctx, event.order);
        } catch (error) {
          Logger.error(`保存用户订单是否会员错误:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'Cancelled'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        await this.couponService.couponCancel(event.ctx, event.order.id);
        if (event.fromState === 'ArrangingPayment') {
          try {
            await this.orderPromotionResultService.cancelMemberPromotionResult(event.ctx, event.order);
          } catch (error) {
            Logger.error(`取消会员优惠结果错误:${error}`);
          }
          try {
            await this.orderPromotionResultService.cancelProductLimitationResult(event.ctx, event.order);
          } catch (error) {
            Logger.error(`取消产品限购结果错误:${error}`);
          }
          // 积分解冻
          try {
            await this.pointsService.cancelOrderPoints(event.ctx, event.order);
          } catch (error) {
            Logger.error(`取消订单积分解冻错误:${error}`);
          }

          // 盲盒订单状态回滚
          try {
            await this.blindBoxOrderService.cancelOrder(event.ctx, event.order);
          } catch (error) {
            Logger.error(`取消订单盲盒订单状态回滚错误:${error}`);
          }

          // 购物金回退
          try {
            await this.shoppingCreditsDeductionActivityService.cancelOrderShoppingCreditsDeductionActivity(
              event.ctx,
              event.order,
            );
          } catch (error) {
            Logger.error(`取消订单购物金回退错误:${error}`);
          }
        } else {
          try {
            await this.distributorService.cancelOrderDistributorDetail(event.ctx, event.order);
          } catch (error) {
            Logger.error(`取消订单扣除分销记录错误:${error}`);
          }
          // 订单关闭并且是积分兑换订单，需要退还积分
          try {
            await this.pointsService.cancelOrderPointsExchange(event.ctx, event.order);
          } catch (error) {
            Logger.error(`取消订单积分退还错误:${error}`);
          }
        }
        // 积分兑换退回可兑换数量
        try {
          await this.pointsService.cancelOrderPointsExchangeCount(event.ctx, event.order);
        } catch (error) {
          Logger.error(`取消订单积分兑换退还可兑换数量错误:${error}`);
        }
      });

    //删除customer时，删除分销商
    this.eventBus
      .ofType(CustomerEvent)
      .pipe(filter(event => event.type === 'deleted'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          const customerId = event.input as ID;
          await this.distributorService.deleteDistributor(event.ctx, customerId);
        } catch (error) {
          Logger.error(`删除分销商错误:${error}`);
        }
      });

    //修改产品SKU时，更新产品SKU更新时间
    this.eventBus
      .ofType(ProductVariantEvent)
      .pipe(filter(event => event.type === 'updated'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.productCustomService.updateProductSkuUpdateTime(event.ctx, event.entity);
        } catch (error) {
          Logger.error(`修改SKU记录修改时间错误:${error}`);
        }
      });

    //修改产品时，更新产品的更新时间
    this.eventBus
      .ofType(ProductEvent)
      .pipe(filter(event => event.type === 'updated'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          await this.productCustomService.updateProductUpdateTime(event.ctx, event.entity);
        } catch (error) {
          Logger.error(`修改产品时记录修改时间错误:${error}`);
        }
      });

    this.eventBus
      .ofType(OrderStateTransitionEvent)
      .pipe(filter(event => event.toState === 'Shipped'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          if (process.env.SWITCH_REPORT_LOGISTICS_TO_WECHAT_TIME) {
            // 如果配置了时间，则在配置的时间之后才执行原上报物流逻辑
            const startTime = new Date(process.env.SWITCH_REPORT_LOGISTICS_TO_WECHAT_TIME);
            // 如果配置的时间小于当前时间，则不执行原上报物流逻辑
            if (startTime <= new Date()) {
              return;
            }
          }
          await this.wechatLogisticsCompanyService.orderShipped(event.ctx, event.order.id);
        } catch (error) {
          Logger.error(`微信物流通知失败:${error}`);
        }
      });

    this.eventBus
      .ofType(ChannelEvent)
      .pipe(filter(event => event.type === 'created'))
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          //创建渠道时创建自动计算优惠方法
          await this.promotionExtendService.channelPromotionCreate(event.entity);
        } catch (error) {
          Logger.error(`渠道创建时创建自动计算优惠方法错误:${error}`);
        }
        try {
          //创建渠道时创建默认的运费模板-包邮
          await this.promotionExtendService.createDefaultShippingMethod(event.entity);
        } catch (error) {
          Logger.error(`渠道创建时创建默认的运费模板-包邮错误:${error}`);
        }

        try {
          //创建渠道时默认创建matomo的站点
          await this.matomoService.createMatomoSiteByChannel(event.entity);
        } catch (error) {
          Logger.error(`创建渠道时默认创建matomo的站点错误:${error}`);
        }
      });

    this.eventBus
      .ofType(MerchantVoluntaryRefundEvent)
      // eslint-disable-next-line @typescript-eslint/no-misused-promises
      .subscribe(async event => {
        try {
          const price = event.merchantVoluntaryRefund.price;
          await this.distributorService.subtractDistributorDetailOrderAmount(event.ctx, event.order, price);
        } catch (error) {
          Logger.error(`后台主动退款扣除分销金额错误:${error}`);
        }
        // 主动退款退还订单支付有礼全部优惠券
        try {
          await this.paymentRewardActivityService.refundOrderPaymentRewardCoupon(event.ctx, event.order.id);
        } catch (error) {
          Logger.error(`主动退款退还订单支付有礼全部优惠券错误:${error}`);
        }
        // 主动退款退还订单购买的全部优惠券
        try {
          await this.payCouponService.refundOrderPayCoupon(event.ctx, event.order.id);
        } catch (error) {
          Logger.error(`主动退款退还订单购买的全部优惠券错误:${error}`);
        }
        // 主动退款完成后判断订单项是否取消。如果是取消则执行限购退回
        try {
          await this.orderPromotionResultService.productLimitationRefund(
            event.ctx,
            event.order,
            event.merchantVoluntaryRefund,
          );
        } catch (error) {
          Logger.error(`主动退款执行限购退回错误:${error}`);
        }
        // 主动退款
        try {
          await this.pointsService.merchantVoluntaryRefund(event.ctx, event.order, event.merchantVoluntaryRefund.price);
        } catch (error) {
          Logger.error(`主动退款积分退还错误:${error}`);
        }
      });

    // this.eventBus
    //   .ofType(AssetEvent)
    //   .pipe(filter(event => event.type === 'created'))
    //   // eslint-disable-next-line @typescript-eslint/no-misused-promises
    //   .subscribe(async event => {
    //     try {
    //       await this.commonService.syncCompressImageJob(event.ctx, event.asset.id);
    //     } catch (error) {
    //       Logger.error(`创建产品图片记录修改时间错误:${error}`);
    //     }
    //   });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    this.eventBus.ofType(ProductEvent).subscribe(async event => {
      try {
        const ctx = event.ctx;
        await this.cacheService.clearProductCache(ctx, event.entity.id);
        const collections = await this.customerProductService.getCollectionsByProductId(
          event.ctx,
          event.entity.id,
          event.ctx.apiType === 'shop',
        );
        if (collections?.length > 0) {
          const connectionIds = collections.map(collection => collection.id);
          if (connectionIds.length > 0) {
            await this.cacheService.removeCache([
              ...connectionIds.map(connectionId => `Query:Product:Collections:${connectionId}:${ctx.channelId}`),
            ]);
          }
        }
      } catch (error) {
        Logger.error(`清除产品缓存错误:${error}`);
      }
    });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    this.eventBus.ofType(CollectionEvent).subscribe(async event => {
      const ctx = event.ctx;
      await this.cacheService.removeCache(`Query:Product:Collections:${event.entity.id}:${ctx.channelId}`);
    });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    this.eventBus.ofType(StockMovementEvent).subscribe(async event => {
      const ctx = event.ctx;
      const stockMovements = event.stockMovements;
      const productIds = [];
      const productVariantIds = [];
      for (const stockMovement of stockMovements) {
        let productVariant = stockMovement.productVariant;
        if (productVariant) {
          const productVariantId = productVariant.id;
          const productId = productVariant.productId;
          if (!productId) {
            productVariant = await this.customerProductVariantService.getVariantByVariantId(
              event.ctx,
              productVariantId,
            );
          }
          if (productVariant?.productId) {
            productIds.push(productVariant.productId);
          }
          productVariantIds.push(productVariantId);
        }
      }
      //productIds去重
      const uniqueProductIds = Array.from(new Set(productIds));
      //productVariantIds去重
      const uniqueProductVariantIds = Array.from(new Set(productVariantIds));
      await this.cacheService.removeCache([
        ...uniqueProductIds.map(productId => `Query:ProductTotalStock:${productId}:${ctx.channelId}`),
        ...uniqueProductVariantIds.map(
          productVariantId => `Query:ProductVariant:StockLevel:${productVariantId}:${ctx.channelId}`,
        ),
      ]);
    });
  }

  static ui: AdminUiExtension = {
    extensionPath: path.join(__dirname, '../src/ui'),

    ngModules: [
      {
        type: 'shared' as const,
        ngModuleFileName: 'ecommerce-common-ui-shared.module.ts',
        ngModuleName: 'EcommerceCommonUISharedModule',
      },
      {
        type: 'lazy' as const,
        route: 'ecommerce',
        ngModuleFileName: 'ecommerce-common-ui.module.ts',
        ngModuleName: 'EcommerceUICommonModule',
      },
      {
        type: 'shared' as const,
        ngModuleFileName: 'ecommerce-common-pipe-shared.module.ts',
        ngModuleName: 'EcommerceCommonPipeSharedModule',
      },
    ],
  };
}
