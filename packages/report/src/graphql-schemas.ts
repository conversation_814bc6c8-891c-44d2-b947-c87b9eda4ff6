import {gql} from 'graphql-tag';

// eslint-disable-next-line
const _scalar = gql`
  scalar DateTime
  scalar JSON
`;

const sharedTypes = gql`
  type ReportTemplate implements Node {
    id: ID!
    templateName: String!
    filterConfig: JSON!
    headerConfig: JSON!
    sqlStatement: String!
    createdAt: DateTime!
    updatedAt: DateTime
    status: Boolean!
  }

  type ReportTemplateList implements PaginatedList {
    items: [ReportTemplate!]!
    totalItems: Int!
  }

  # 报表查询结果
  type ReportQueryResult {
    headers: [HeaderMeta!]!
    rows: [JSON!]!
    stats: ExecutionStats!
  }

  # 表头元数据
  type HeaderMeta {
    field: String!
    name: String!
  }

  # 执行统计信息
  type ExecutionStats {
    executionTime: Int!
    totalRows: Int!
  }

  # 报表错误代码
  enum ReportErrorCode {
    SQL_INJECTION_RISK
    HEADER_MISMATCH
    TIMEOUT
    INVALID_FILTER
  }

  # 报表分页参数
  input ReportPagination {
    page: Int!
    pageSize: Int!
  }

  # 报表过滤条件
  input ReportFilterInput {
    field: String!
    operator: String!
    value: JSON
  }

  # 报表查询参数
  input ReportQueryInput {
    templateId: ID!
    filters: [ReportFilterInput!]
    pagination: ReportPagination
  }

  # 报表模板输入
  input ReportTemplateInput {
    id: ID
    templateName: String!
    filterConfig: JSON!
    headerConfig: JSON!
    sqlStatement: String!
  }

  # 报表模板列表选项
  input ReportTemplateListOptions {
    skip: Int
    take: Int
    sort: ReportTemplateSortParameter
    filter: ReportTemplateFilterParameter
    filterOperator: LogicalOperator
  }

  input ReportTemplateSortParameter {
    id: SortOrder
    templateName: SortOrder
    createdAt: SortOrder
    updatedAt: SortOrder
  }

  input ReportTemplateFilterParameter {
    id: IDOperators
    templateName: StringOperators
    createdAt: DateOperators
    updatedAt: DateOperators
  }
`;

const adminSchema = gql`
  extend type Query {
    reportTemplates(options: ReportTemplateListOptions): ReportTemplateList!
    reportTemplate(id: ID!): ReportTemplate
    executeReport(input: ReportQueryInput!): ReportQueryResult!
  }

  extend type Mutation {
    createReportTemplate(input: ReportTemplateInput!): ReportTemplate!
    updateReportTemplate(input: ReportTemplateInput!): ReportTemplate!
    deleteReportTemplate(id: ID!): Boolean!
  }
`;

export const adminSchemaExtensions = gql`
  ${sharedTypes}
  ${adminSchema}
`;
