import {PluginCommonModule, VendurePlugin} from '@vendure/core';
import {ReportTemplateResolver} from './api';
import {ReportTemplate} from './entities';
import {adminSchemaExtensions} from './graphql-schemas';
import {
  CreateReportTemplatePermission,
  DeleteReportTemplatePermission,
  ExecuteReportPermission,
  ReadReportTemplatePermission,
  UpdateReportTemplatePermission,
} from './permission-definition';
import {ReportTemplateService} from './service';

@VendurePlugin({
  imports: [PluginCommonModule],
  entities: [ReportTemplate],
  providers: [ReportTemplateService],
  adminApiExtensions: {
    schema: adminSchemaExtensions,
    resolvers: [ReportTemplateResolver],
  },
  configuration: config => {
    config.authOptions.customPermissions.push(
      CreateReportTemplatePermission,
      ReadReportTemplatePermission,
      UpdateReportTemplatePermission,
      DeleteReportTemplatePermission,
      ExecuteReportPermission,
    );
    return config;
  },
})
export class ReportPlugin {}
