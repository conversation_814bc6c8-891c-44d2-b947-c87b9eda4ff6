import {Injectable, Logger, OnModuleDestroy, OnModuleInit} from '@nestjs/common';
import {
  ID,
  ListQueryBuilder,
  ListQueryOptions,
  RelationPaths,
  RequestContext,
  TransactionalConnection,
  UserInputError,
} from '@vendure/core';
import {DataSource} from 'typeorm';
import {ReportTemplate} from '../entities';
import {ReportFilterInput, ReportPagination, ReportQueryInput, ReportTemplateInput} from '../generated-admin-types';
import {FilterConfig, HeaderConfig, HeaderMeta} from '../types/report-template.types';

@Injectable()
export class ReportTemplateService implements OnModuleDestroy, OnModuleInit {
  private readOnlyConnection: DataSource | null = null;

  constructor(private connection: TransactionalConnection, private listQueryBuilder: ListQueryBuilder) {}
  /**
   * 模块初始化时建立只读数据库连接
   */
  async onModuleInit() {
    try {
      Logger.log('开始初始化只读数据库连接...');
      await this.getReadOnlyConnection();
      Logger.log('只读数据库连接初始化完成');
    } catch (error) {
      Logger.error('只读数据库连接初始化失败', error.stack);
      // 可以选择在这里终止应用启动，或者实现重试机制
    }
  }

  /**
   * 清理资源
   */
  async onModuleDestroy() {
    if (this.readOnlyConnection) {
      try {
        await this.readOnlyConnection.destroy();
        console.log('只读数据库连接已关闭');
      } catch (error) {
        console.error('关闭只读数据库连接失败:', error);
      }
    }
  }

  /**
   * 创建只读数据库连接
   * 参考 vendure-config-admin.ts 中的 dbConnectionOptions 配置
   */
  private async getReadOnlyConnection(): Promise<DataSource> {
    if (!this.readOnlyConnection?.isInitialized) {
      try {
        // 参考现有 Vendure 配置，创建只读数据库连接配置
        const readOnlyConfig = {
          type: 'mysql' as const,
          host: process.env.DB_HOST,
          port: +(process.env.DB_PORT || 3306),
          username: process.env.DB_USERNAME,
          password: process.env.DB_PASSWORD,
          database: process.env.DB_NAME,
          timezone: '+08:00',
          charset: 'utf8mb4',
          poolSize: 10, // 较小的连接池，适合只读查询
          // 禁用同步和迁移，确保只读安全
          synchronize: false,
          migrationsRun: false,
          logging: false,
          // 添加连接超时配置
          acquireTimeout: 30000,
          timeout: 30000,
        };

        this.readOnlyConnection = new DataSource(readOnlyConfig);
        await this.readOnlyConnection.initialize();
      } catch (error) {
        console.error('创建只读数据库连接失败:', error);
        throw new UserInputError('无法连接到只读数据库');
      }
    }
    return this.readOnlyConnection;
  }

  /**
   * 查询所有报表模板
   */
  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<ReportTemplate>,
    relations?: RelationPaths<ReportTemplate>,
  ) {
    const qb = this.listQueryBuilder.build(ReportTemplate, options, {
      ctx,
      relations: relations,
    });

    const [items, totalItems] = await qb.getManyAndCount();

    // 打印查询结果，用于调试
    console.log('=== 报表模板查询接口请求参数 ===');
    console.log('RequestContext:', ctx);
    console.log('=== 报表模板查询结果 ===');
    console.log('items:', JSON.stringify(items, null, 2));

    // 移除了createdBy相关逻辑

    return {
      items,
      totalItems,
    };
  }

  /**
   * 查询单个报表模板
   */
  async findOne(
    ctx: RequestContext,
    id: ID,
    options?: ListQueryOptions<ReportTemplate>,
    relations?: RelationPaths<ReportTemplate>,
  ) {
    const qb = this.listQueryBuilder.build(ReportTemplate, options, {
      ctx,
      relations: relations,
    });
    qb.andWhere(`${qb.alias}.id = :id`, {id});
    const reportTemplate = await qb.getOne();

    // 移除了createdBy相关逻辑

    return reportTemplate;
  }

  /**
   * 创建报表模板
   */
  async create(ctx: RequestContext, input: ReportTemplateInput) {
    // 解析JSON字符串为对象
    const filterConfig = typeof input.filterConfig === 'string' ? JSON.parse(input.filterConfig) : input.filterConfig;
    const headerConfig = typeof input.headerConfig === 'string' ? JSON.parse(input.headerConfig) : input.headerConfig;

    const reportTemplate = new ReportTemplate({
      templateName: input.templateName,
      filterConfig: filterConfig as FilterConfig,
      headerConfig: headerConfig as HeaderConfig,
      sqlStatement: input.sqlStatement,
      status: false, // 默认设置为未测试状态
    });

    // 验证SQL语句是否包含{whereClause}占位符
    this.validateSqlStatement(reportTemplate.sqlStatement);

    // 验证表头配置与SQL结果集一致性
    this.validateHeaderConfig(reportTemplate.headerConfig, reportTemplate.sqlStatement);

    return this.connection.getRepository(ctx, ReportTemplate).save(reportTemplate);
  }

  /**
   * 更新报表模板
   */
  async update(ctx: RequestContext, input: ReportTemplateInput) {
    const reportTemplate = await this.findOne(ctx, input.id as string);
    if (!reportTemplate) {
      throw new UserInputError(`报表模板不存在: ${input.id}`);
    }

    // 解析JSON字符串为对象
    const filterConfig = typeof input.filterConfig === 'string' ? JSON.parse(input.filterConfig) : input.filterConfig;
    const headerConfig = typeof input.headerConfig === 'string' ? JSON.parse(input.headerConfig) : input.headerConfig;

    reportTemplate.templateName = input.templateName;
    reportTemplate.filterConfig = filterConfig as FilterConfig;
    reportTemplate.headerConfig = headerConfig as HeaderConfig;
    reportTemplate.sqlStatement = input.sqlStatement;
    reportTemplate.status = false; // 修改模板后重置为未测试状态

    // 验证SQL语句是否包含{whereClause}占位符
    this.validateSqlStatement(reportTemplate.sqlStatement);

    // 验证表头配置与SQL结果集一致性
    this.validateHeaderConfig(reportTemplate.headerConfig, reportTemplate.sqlStatement);

    return this.connection.getRepository(ctx, ReportTemplate).save(reportTemplate);
  }

  /**
   * 删除报表模板
   */
  async delete(ctx: RequestContext, id: ID) {
    const reportTemplate = await this.findOne(ctx, id);
    if (!reportTemplate) {
      throw new UserInputError(`报表模板不存在: ${id}`);
    }

    await this.connection.getRepository(ctx, ReportTemplate).remove(reportTemplate);
    return true;
  }

  /**
   * 执行报表查询
   */
  async executeReport(ctx: RequestContext, input: ReportQueryInput) {
    const startTime = Date.now();
    const reportTemplate = await this.findOne(ctx, input.templateId);
    if (!reportTemplate) {
      throw new UserInputError(`报表模板不存在: ${input.templateId}`);
    }

    // 解析过滤条件
    const parsedFilters = this.parseFilters(
      input.filters || [],
      reportTemplate.filterConfig,
      reportTemplate.sqlStatement,
    );

    // 打印过滤条件，用于调试
    console.log('=== 过滤条件 ===');
    console.log(parsedFilters.whereClause);

    // 构建动态SQL
    const sql = this.buildDynamicSql(reportTemplate.sqlStatement, parsedFilters.whereClause);

    // 打印原始SQL语句，用于调试
    Logger.warn(`原始SQL查询: ${sql}`, 'ReportTemplateService');
    Logger.warn(`过滤条件: ${JSON.stringify(parsedFilters)}`, 'ReportTemplateService');

    // 直接控制台输出
    console.log('=== 报表模板SQL查询 ===');
    console.log(`原始SQL查询: ${sql}`);
    console.log(`过滤条件: ${JSON.stringify(parsedFilters)}`);

    // 执行SQL查询
    const queryResult = await this.executeSql(ctx, sql, parsedFilters.parameters, input.pagination || undefined);

    // 添加调试日志
    Logger.debug(`executeReport 查询结果: ${JSON.stringify(queryResult)}`, 'ReportTemplateService');

    // 解析表头
    const headers = this.parseHeaderConfig(reportTemplate.headerConfig);

    // 计算执行时间
    const executionTime = Date.now() - startTime;

    // 确保返回的rows是数组
    // 直接使用queryResult作为rows，因为executeSql方法已经处理好了结果格式
    const resultRows = queryResult.rows;

    // 添加调试日志
    Logger.warn(
      `executeReport 返回结果: headers=${headers.length}, rows=${resultRows ? resultRows.length : 0}, totalRows=${
        queryResult.totalRows
      }`,
      'ReportTemplateService',
    );

    // 直接控制台输出
    console.log('=== 报表查询结果 ===');
    console.log(
      `headers=${headers.length}, rows=${resultRows ? resultRows.length : 0}, totalRows=${queryResult.totalRows}`,
    );
    console.log('原始查询结果:', queryResult);
    console.log('处理后的行数据:', resultRows);

    // 更新报表模板的status字段
    // 如果查询结果中有数据，则将status设为true，否则设为false
    const hasData = resultRows && resultRows.length > 0;
    reportTemplate.status = hasData;
    await this.connection.getRepository(ctx, ReportTemplate).save(reportTemplate);

    // 记录状态更新日志
    Logger.warn(
      `报表模板状态已更新: id=${reportTemplate.id}, status=${reportTemplate.status}`,
      'ReportTemplateService',
    );
    console.log(`报表模板状态已更新: id=${reportTemplate.id}, status=${reportTemplate.status}`);

    return {
      headers,
      rows: resultRows,
      stats: {
        executionTime,
        totalRows: queryResult.totalRows,
      },
    };
  }

  /**
   * 验证SQL语句
   */
  private validateSqlStatement(sqlStatement: string) {
    // 检查SQL语句是否以SELECT开头（忽略大小写和前导空格）
    if (!/^\s*SELECT\s+/i.test(sqlStatement)) {
      throw new UserInputError('SQL语句必须是SELECT查询', {
        code: 'INVALID_SQL_TYPE',
      });
    }

    // 检查SQL语句是否包含{whereClause}占位符
    if (!sqlStatement.includes('{whereClause}')) {
      throw new UserInputError('SQL语句必须包含{whereClause}占位符，用于添加补充条件');
    }

    // 检查SQL语句是否包含WHERE子句
    if (!/\bWHERE\b/i.test(sqlStatement)) {
      throw new UserInputError('SQL语句必须包含WHERE子句，{whereClause}仅用于添加补充条件');
    }

    // 检查SQL注入风险
    const riskPatterns = [
      /;\s*DROP/i,
      /;\s*DELETE/i,
      /;\s*UPDATE/i,
      /;\s*INSERT/i,
      /;\s*ALTER/i,
      /;\s*CREATE/i,
      /EXECUTE\s+/i,
      /EXEC\s+/i,
      /INTO\s+OUTFILE/i,
      /INTO\s+DUMPFILE/i,
      // 禁止多语句查询
      /;\s*SELECT/i,
      // 禁止使用UNION，防止SQL注入
      /UNION\s+ALL/i,
      /UNION\s+SELECT/i,
    ];

    for (const pattern of riskPatterns) {
      if (pattern.test(sqlStatement)) {
        throw new UserInputError('SQL语句存在安全风险', {
          code: 'SQL_INJECTION_RISK',
        });
      }
    }
  }

  /**
   * 验证表头配置
   */
  private validateHeaderConfig(headerConfig: HeaderConfig, sqlStatement: string) {
    try {
      if (!Array.isArray(headerConfig)) {
        throw new UserInputError('表头配置必须是数组');
      }

      // 验证每个表头项是否包含必要的字段
      for (const header of headerConfig) {
        if (!header.field || !header.name) {
          throw new UserInputError('表头配置项必须包含field和name字段');
        }
      }
    } catch (error) {
      throw new UserInputError(`表头配置验证失败: ${error.message}`);
    }
  }

  /**
   * 解析过滤条件
   */
  private parseFilters(filters: ReportFilterInput[], filterConfig: FilterConfig, sqlStatement?: string) {
    try {
      const whereConditions: string[] = [];
      const parameters: Record<string, any> = {};

      // 表名映射，用于解决列名歧义问题
      // 这里我们不再使用动态生成的表名映射，而是让用户在filterConfig中指定完整的字段名（包括表名）
      console.log('=== 过滤条件 ===');
      console.log(filters);

      for (const filter of filters) {
        // 验证过滤条件是否在配置中定义
        const filterDef = filterConfig.find(f => f.field === filter.field);
        if (!filterDef) {
          throw new UserInputError(`未定义的过滤条件: ${filter.field}`);
        }

        // 根据操作符构建条件
        // 处理字段名，移除表名部分用于参数名
        const fieldParts = filter.field.split('.');
        const paramName = fieldParts[fieldParts.length - 1]; // 取最后一部分作为参数名

        switch (filter.operator) {
          case 'eq':
            whereConditions.push(`${filter.field} = :${paramName}`);
            parameters[paramName] = filter.value;
            break;
          case 'neq':
            whereConditions.push(`${filter.field} != :${paramName}`);
            parameters[paramName] = filter.value;
            break;
          case 'gt':
            whereConditions.push(`${filter.field} > :${paramName}`);
            parameters[paramName] = filter.value;
            break;
          case 'gte':
            whereConditions.push(`${filter.field} >= :${paramName}`);
            parameters[paramName] = filter.value;
            break;
          case 'lt':
            whereConditions.push(`${filter.field} < :${paramName}`);
            parameters[paramName] = filter.value;
            break;
          case 'lte':
            whereConditions.push(`${filter.field} <= :${paramName}`);
            parameters[paramName] = filter.value;
            break;
          case 'like':
            whereConditions.push(`${filter.field} LIKE :${paramName}`);
            parameters[paramName] = `%${filter.value}%`;
            break;
          case 'in':
            if (Array.isArray(filter.value)) {
              whereConditions.push(`${filter.field} IN (:...${paramName})`);
              parameters[paramName] = filter.value;
            } else {
              throw new UserInputError(`in操作符的值必须是数组: ${filter.field}`);
            }
            break;
          case 'between':
            if (Array.isArray(filter.value) && filter.value.length === 2) {
              // 使用字段名作为参数名前缀，避免参数名冲突
              const startParam = `${paramName}Start`;
              const endParam = `${paramName}End`;
              whereConditions.push(`${filter.field} BETWEEN :${startParam} AND :${endParam}`);
              parameters[startParam] = filter.value[0];
              parameters[endParam] = filter.value[1];
            } else {
              throw new UserInputError(`between操作符的值必须是包含两个元素的数组: ${filter.field}`);
            }
            break;
          default:
            throw new UserInputError(`不支持的操作符: ${filter.operator}`);
        }
      }

      return {
        whereClause: whereConditions.length > 0 ? whereConditions.join(' AND ') : '',
        parameters,
      };
    } catch (error) {
      throw new UserInputError(`过滤条件解析失败: ${error.message}`, {
        code: 'INVALID_FILTER',
      });
    }
  }

  /**
   * 构建动态SQL
   * 智能处理补充条件的替换，根据前面是否有条件决定是否添加AND
   */
  private buildDynamicSql(sqlTemplate: string, conditions: string) {
    // 如果没有补充条件，直接替换占位符为空字符串
    if (!conditions || conditions === '') {
      return sqlTemplate.replace('{whereClause}', '');
    }

    // 确保条件不包含WHERE关键字
    const cleanConditions = conditions.replace(/^\s*WHERE\s+/i, '');

    if (!cleanConditions) {
      return sqlTemplate.replace('{whereClause}', '');
    }

    // 查找{whereClause}占位符的位置
    const placeholderIndex = sqlTemplate.indexOf('{whereClause}');
    if (placeholderIndex === -1) {
      return sqlTemplate;
    }

    // 提取占位符前的SQL片段
    const sqlBeforePlaceholder = sqlTemplate.substring(0, placeholderIndex);

    // 查找最近的WHERE关键字位置
    const whereMatch = sqlBeforePlaceholder.match(/\bWHERE\b/gi);
    if (!whereMatch) {
      // 如果没有WHERE关键字，直接添加条件
      return sqlTemplate.replace('{whereClause}', cleanConditions);
    }

    // 找到最后一个WHERE的位置
    const lastWhereIndex = sqlBeforePlaceholder.lastIndexOf(whereMatch[whereMatch.length - 1]);

    // 提取WHERE后面到占位符之间的内容
    const contentAfterWhere = sqlBeforePlaceholder.substring(lastWhereIndex + 5).trim(); // 5是"WHERE"的长度

    // 判断WHERE后面是否有实际的条件
    // 如果只有空白字符、注释或者没有内容，说明没有条件
    const hasConditionsAfterWhere =
      contentAfterWhere &&
      !/^\s*$/.test(contentAfterWhere) && // 不是空白
      !/^\s*--.*$/.test(contentAfterWhere) && // 不是单行注释
      !/^\s*\/\*.*\*\/\s*$/.test(contentAfterWhere); // 不是多行注释

    // 打印调试信息
    console.log('=== buildDynamicSql 调试信息 ===');
    console.log('WHERE后面的内容:', `"${contentAfterWhere}"`);
    console.log('是否有条件:', hasConditionsAfterWhere);
    console.log('补充条件:', cleanConditions);

    if (hasConditionsAfterWhere) {
      // 如果WHERE后面有条件，使用AND连接
      console.log('使用AND连接条件');
      return sqlTemplate.replace('{whereClause}', ` AND ${cleanConditions}`);
    } else {
      // 如果WHERE后面没有条件，直接添加条件（不加AND）
      console.log('直接添加条件（不加AND）');
      return sqlTemplate.replace('{whereClause}', ` ${cleanConditions}`);
    }
  }

  /**
   * 执行SQL查询
   */
  private async executeSql(
    ctx: RequestContext,
    sql: string,
    parameters: Record<string, any>,
    pagination?: ReportPagination,
  ) {
    try {
      // 设置查询超时
      const timeout = 30000; // 30秒
      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('查询超时')), timeout));

      // 添加分页
      let paginatedSql = sql;
      if (pagination) {
        const offset = (pagination.page - 1) * pagination.pageSize;

        // 确保SQL语句末尾有空格，避免语法错误
        if (!paginatedSql.endsWith(' ')) {
          paginatedSql += ' ';
        }

        // 添加LIMIT和OFFSET
        paginatedSql = `${paginatedSql}LIMIT ${pagination.pageSize} OFFSET ${offset}`;

        // 打印分页SQL，用于调试
        console.log('=== 分页SQL ===');
        console.log(paginatedSql);
      }

      // 将命名参数转换为问号占位符
      const {sql: preparedSql, params: preparedParams} = this.convertNamedParamsToQuestionMarks(
        paginatedSql,
        parameters,
      );

      // 打印SQL语句和参数，用于调试
      Logger.warn(`SQL参数: ${JSON.stringify(preparedParams)}`, 'ReportTemplateService');

      // 直接控制台输出
      console.log('=== 执行SQL查询 ===');
      console.log(`执行SQL查询: ${preparedSql}`);
      console.log(`SQL参数: ${JSON.stringify(preparedParams)}`);

      // 打印参数拼接后的完整SQL语句
      const fullSql = this.getFullSql(preparedSql, preparedParams);
      console.log('=== 完整SQL语句 ===');
      console.log(fullSql);

      // 使用只读数据库连接执行查询
      const readOnlyConn = await this.getReadOnlyConnection();
      const queryPromise = readOnlyConn.query(preparedSql, preparedParams);
      const queryResult = await Promise.race([queryPromise, timeoutPromise]);

      Logger.warn(`执行SQL查询完成，返回 ${queryResult.length} 行数据`, 'ReportTemplateService');

      console.log(`执行SQL查询完成，返回 ${queryResult.length} 行数据`);

      // 添加调试日志
      Logger.debug(`查询结果: ${JSON.stringify(queryResult)}`, 'ReportTemplateService');

      // 直接控制台输出查询结果
      console.log('=== 数据库查询结果 ===');
      console.log('查询结果类型:', typeof queryResult);
      console.log('查询结果是否为数组:', Array.isArray(queryResult));
      console.log('查询结果行数:', queryResult.length);

      // TypeORM DataSource 返回的结果已经是数组格式
      const rows = queryResult;

      // 获取总行数，使用只读数据库连接
      const {sql: preparedCountSql, params: preparedCountParams} = this.convertNamedParamsToQuestionMarks(
        `SELECT COUNT(*) AS total FROM (${sql}) AS count_query`,
        parameters,
      );
      const countResult = await readOnlyConn.query(preparedCountSql, preparedCountParams);

      // 添加调试日志
      Logger.debug(`计数结果: ${JSON.stringify(countResult)}`, 'ReportTemplateService');

      // 确保计数结果正确解析
      const totalRows =
        countResult?.[0] && typeof countResult[0].total !== 'undefined'
          ? parseInt(countResult[0].total, 10)
          : Array.isArray(rows)
          ? rows.length
          : 0;

      return {
        rows,
        totalRows,
      };
    } catch (error) {
      Logger.error(`执行SQL查询失败: ${error.message}`, error.stack, 'ReportTemplateService');
      if (error.message === '查询超时') {
        throw new UserInputError('查询超时', {code: 'TIMEOUT'});
      }
      throw new UserInputError(`执行SQL查询失败: ${error.message}`);
    }
  }

  /**
   * 解析表头配置
   */
  private parseHeaderConfig(headerConfig: HeaderConfig): HeaderMeta[] {
    try {
      return headerConfig.map(header => ({
        field: header.field,
        name: header.name,
      }));
    } catch (error) {
      throw new UserInputError(`表头配置解析失败: ${error.message}`);
    }
  }

  /**
   * 将命名参数转换为问号占位符
   * 例如：将 "WHERE createdAt BETWEEN :createdAtStart AND :createdAtEnd"
   * 转换为 "WHERE createdAt BETWEEN ? AND ?"
   */
  private convertNamedParamsToQuestionMarks(sql: string, params: Record<string, any>) {
    let convertedSql = sql;
    const convertedParams: any[] = [];

    // 正则表达式匹配命名参数（以冒号开头的标识符）
    const namedParamRegex = /:([\w]+)/g;
    let match;

    // 收集所有命名参数
    const namedParams: string[] = [];
    while ((match = namedParamRegex.exec(sql)) !== null) {
      namedParams.push(match[1]);
    }

    // 替换命名参数为问号，并按顺序收集参数值
    for (const paramName of namedParams) {
      convertedSql = convertedSql.replace(`:${paramName}`, '?');
      if (params[paramName] !== undefined) {
        convertedParams.push(params[paramName]);
      } else {
        // 如果参数不存在，抛出错误
        throw new UserInputError(`缺少参数: ${paramName}`);
      }
    }

    return {sql: convertedSql, params: convertedParams};
  }

  /**
   * 动态生成表名映射
   * 根据SQL语句和过滤配置分析字段所属的表
   */
  // private generateTableMap(filterConfigObj: any[], sqlStatement?: string): Record<string, string> {
  // 默认映射，适用于购物金统计报表
  // const defaultMap: Record<string, string> = {
  //   customerId: 'history',
  //   phoneNumber: 'customer',
  //   issuedAmount: 'history',
  //   deductedAmount: 'history',
  //   totalAmount: 'history',
  //   isMember: 'member',
  //   createdAt: 'history',
  // };

  // 如果没有提供SQL语句，返回默认映射
  // if (!sqlStatement) {
  //   return defaultMap;
  // }

  //   // 尝试从SQL语句中分析字段所属的表
  //   const tableMap: Record<string, string> = {};

  //   // 提取SQL中的字段别名定义，格式如: table.field AS alias
  //   const aliasRegex = /(\w+)\.(\w+)\s+AS\s+(\w+)/gi;
  //   let match;

  //   // 确保sqlStatement不为undefined
  //   const sql = sqlStatement || '';
  //   while ((match = aliasRegex.exec(sql)) !== null) {
  //     const [, tableName, , alias] = match;
  //     tableMap[alias] = tableName;
  //   }

  //   // 打印表名映射，用于调试
  //   console.log('=== 表名映射 ===');
  //   console.log(tableMap);

  //   return tableMap;
  // }

  /**
   * 获取参数拼接后的完整SQL语句
   * 注意：此方法仅用于调试，不应在生产环境中使用
   */
  private getFullSql(sql: string, params: any[]): string {
    if (!params || params.length === 0) {
      return sql;
    }

    let fullSql = sql;
    let paramIndex = 0;

    // 替换所有问号为实际参数值
    while (fullSql.includes('?') && paramIndex < params.length) {
      const param = params[paramIndex];
      let paramValue: string;

      // 根据参数类型进行格式化
      if (param === null || param === undefined) {
        paramValue = 'NULL';
      } else if (typeof param === 'string') {
        // 字符串需要加引号并转义单引号
        paramValue = `'${param.replace(/'/g, "''")}'`;
      } else if (param instanceof Date) {
        // 日期转换为ISO字符串并加引号
        paramValue = `'${param.toISOString()}'`;
      } else if (Array.isArray(param)) {
        // 数组转换为逗号分隔的列表
        paramValue = param.map(item => (typeof item === 'string' ? `'${item.replace(/'/g, "''")}'` : item)).join(', ');
      } else {
        // 其他类型直接转换为字符串
        paramValue = String(param);
      }

      // 替换第一个问号
      fullSql = fullSql.replace('?', paramValue);
      paramIndex++;
    }

    return fullSql;
  }
}
